import BreadCrumbs from '../../../components/UI/BreadCrumbs/BreadCrumbs'
import Image from 'next/image'
import { PageContainer } from '../../../components/UI/Page/PageContainer/PageContainer'
import style from '../index.module.css'
import Backarrow from '../../../svg/metronic/back_metronic.svg'
import certificate from '../../../svg/metronic/certificate.png'
import download from '../../../svg/metronic/download.svg'
import clsx from 'clsx'

export default function Certificates() {
  return (
    <PageContainer theme="metronic">
      <div className="flex flex-column gap-5">
        <div className="flex align-items-center gap-2 mt-1">
          <Image src={Backarrow} alt="Back" />
          <BreadCrumbs
            title="My Certificates"
            breadcrumbItems={[{ label: 'My Certificates' }]}
            theme="metronic"
          />
        </div>
        <div className={clsx('p-4 gap-4 flex flex-column', style.pageCard)}>
          <div className="text-right">
            <Image src={download} alt="download" />
          </div>
          <div className="text-center">
            <Image src={certificate} alt="certificate" />
          </div>
        </div>
      </div>
    </PageContainer>
  )
}
