'use client'

import { useState, useRef, useEffect } from 'react'
import { Document, Page, pdfjs } from 'react-pdf'
import 'react-pdf/dist/esm/Page/AnnotationLayer.css'
import But<PERSON> from '../Button/Button'
import { ConditionalDisplay } from '../ConditionalDisplay/ConditionalDisplay'
import Loader from '../Loader/Loader'

pdfjs.GlobalWorkerOptions.workerSrc = `//unpkg.com/pdfjs-dist@${pdfjs.version}/build/pdf.worker.min.mjs`

export default function PdfViewer({
  fileUrl,
  pfdCompleteCallback,
  loading,
  Instructor = false,
}) {
  const [numPages, setNumPages] = useState(null)
  const [currentPage, setCurrentPage] = useState(1)
  const [isScrolledToBottom, setIsScrolledToBottom] = useState(false)
  const scrollRef = useRef(null)
  const onDocumentLoadSuccess = ({ numPages }) => {
    console.log('Number of pages:', numPages)
    setNumPages(numPages)
    setCurrentPage(1) // Reset to first page on load
  }

  const nextPage = () => {
    if (currentPage < numPages) {
      setCurrentPage((prev) => prev + 1)
    }
  }

  const prevPage = () => {
    if (currentPage > 1) {
      setCurrentPage((prev) => prev - 1)
    }
  }
  const handleScroll = () => {
    const container = scrollRef.current
    if (container) {
      const isBottom =
        container.scrollHeight - container.scrollTop <=
        container.clientHeight + 10
      setIsScrolledToBottom(isBottom)
    }
  }

  useEffect(() => {
    const container = scrollRef.current
    if (container) {
      container.addEventListener('scroll', handleScroll)
    }
    return () => {
      if (container) {
        container.removeEventListener('scroll', handleScroll)
      }
    }
  }, [])
  return (
    <div className="flex flex-column items-center">
      <div
        className="scrollable-container"
        style={{ height: Instructor ? '70vh' : '60vh', overflowY: 'auto' }}
        ref={scrollRef}
      >
        <Document
          file={fileUrl}
          onLoadSuccess={onDocumentLoadSuccess}
          loading={
            <div
              className="flex justify-content-center align-items-center w-full relative"
              style={{ height: Instructor ? '70vh' : '60vh' }}
            >
              <Loader />
            </div>
          }
        >
          <Page pageNumber={currentPage} width={600} loading={null} />
        </Document>
      </div>
      <ConditionalDisplay condition={!Instructor}>
        <div className="flex justify-content-end mt-2 gap-4 py-5 w-full">
          <ConditionalDisplay condition={currentPage > 1}>
            <Button
              label="Previous Page"
              theme="metronic"
              onClick={prevPage}
              disabled={currentPage === 1}
              variant="outline"
            />
          </ConditionalDisplay>

          <Button
            label={currentPage === numPages ? 'Complete' : 'Next Page'}
            theme="metronic"
            onClick={currentPage === numPages ? pfdCompleteCallback : nextPage}
            disabled={!isScrolledToBottom}
            loading={loading}
          />
        </div>
      </ConditionalDisplay>
    </div>
  )
}
