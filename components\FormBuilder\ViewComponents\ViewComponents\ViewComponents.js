import ViewComponentContainer from './ViewComponentContainer/ViewComponentContainer'
import clsx from 'clsx'
import NextImage from 'next/image'
import LexicalEditor from '../../../LexicalEditor/LexicalEditor'
import ReadonlyLexicalEditor from '../../../LexicalEditor/ReadonlyLexicalEditor/ReadonlyLexicalEditor'
import viewStyles from './ViewComponents.module.css'
import textStyles from '../../SharedComponents/Text/Text.module.css'
import headerAndImageStyles from '../../SharedComponents/HeaderAndImage/HeaderAndImage.module.css'
import calendarStyles from '../../SharedComponents/Calendar/Calendar.module.css'
import numberStyles from '../../SharedComponents/Number/Number.module.css'
import textareaStyles from '../../SharedComponents/Textarea/Textarea.module.css'
import maskStyles from '../../SharedComponents/Mask/Mask.module.css'
import dropdownStyles from '../../SharedComponents/Dropdown/Dropdown.module.css'
import multiselectStyles from '../../SharedComponents/Multiselect/Multiselect.module.css'
import fileStyles from '../../SharedComponents/File/File.module.css'
import labelStyles from '../../SharedComponents/LabelContainer/LabelContainer.module.css'
import paymentStyles from '../../SharedComponents/Payments/Payments.module.css'
import Errors from '../../SharedComponents/Errors/Errors'
import Label from '../../SharedComponents/Label/Label'
import Subtitle from '../../SharedComponents/Subtitle/Subtitle'
import LabelContainer from '../../SharedComponents/LabelContainer/LabelContainer'
import InputsContainer from '../../SharedComponents/InputsContainer/InputsContainer'
import ComponentContainer from '../../SharedComponents/ComponentContainer/ComponentContainer'
import AlignmentContainer from '../../SharedComponents/AlignmentContainer/AlignmentContainer'
import SecondaryButton from '../../../UI/SecondaryButton/SecondaryButton'
import CheckBox from '../../../UI/Input/CheckBox/CheckBox'
import UrlFileInput from '../../../UI/Input/FileInput/UrlFileInput'
import TermsAndConditionsModal from '../../../UI/TermsAndConditionsModal/TermsAndConditionsModal'
import JsTable from '../../SharedComponents/JsTable/JsTable'
import PrimaryButton from '../../../UI/PrimaryButton/PrimaryButton'
import React, { createElement, useState, useEffect, useCallback, useRef, useContext, useMemo } from 'react'
import { ESignPreferenceType } from '../../../../utillites/enums'
import { OnBoardingEmployeeOptions } from '../../../OnBoarding/OnBoardingEmployeeOptions/OnBoardingEmployeeOptions'
import { InputText } from 'primereact/inputtext'
import { Calendar } from 'primereact/calendar'
import { InputNumber } from 'primereact/inputnumber'
import { InputTextarea } from 'primereact/inputtextarea'
import { InputMask } from 'primereact/inputmask'
import { Dropdown } from 'primereact/dropdown'
import { MultiSelect } from 'primereact/multiselect'
import { AutoComplete } from 'primereact/autocomplete'
import { Rating } from 'primereact/rating'
import { saveAs } from 'file-saver'
import { useRouter } from 'next/router'
import { useMsal, useAccount, useMsalAuthentication } from '@azure/msal-react'
import { RadioButtons } from '../../../UI/RadioButtons/RadioButtons'
import { Checkboxes } from '../../../UI/Checkboxes/Checkboxes'
import { FileInput } from '../../SharedComponents/File/FileUpload/FileUpload'
import { NumberScale } from '../../Settings/UI/NumberScale/NumberScale'
import { InteractionType } from '@azure/msal-browser'
import { dataverseApiRequest } from '../../../../src/msalConfig'
import { MaskPermissionInput } from '../../../UI/MaskPermissionInput/MaskPermissionInput'
import { TableComponent } from '../../SharedComponents/TableComponent/TableComponent'
import { axiosGet } from '../../../../helpers/Axios'
import { useDropdownSorting } from '../../../../hooks/useDropdownSorting'
import { useCalculatedFieldInputs } from '../../../../hooks/useCalculatedFieldInputs'
import { DataTable } from 'primereact/datatable'
import { Column } from 'primereact/column'
import { useDashboard } from '../../../../hooks/useDashboard'
import useUtilityFunctions from '../../../../hooks/useUtilityFunctions'
import Accordion from '../../SharedComponents/Accordion/Accordion'
import { AdvancedFileUpload } from '../../SharedComponents/File/AdvancedFileUpload/AdvancedFileUpload'
import { getAllItemList, getFormSubmissionsFiltered } from '../../../../api/apiCalls'
import { FormSubmissionOptionTemplate } from '../../../UI/AsyncDropdowns/ObjectLinkDropdown/ObjectLinkDropdown'
import Modal from '../../../UI/Modal/Modal'
import style from '../../../../styles/document/esign.module.css'
import star from '../../../../svg/star.svg'
import SignatureCanvas from 'react-signature-canvas'
import { TabPanel, TabView } from 'primereact/tabview'
import { Button } from 'primereact/button'
import Image from 'next/image'
import UserProfileContext from '../../../../public/UserProfileContext/UserProfileContext'
import {
  usePostESignaturePreference,
  useGetAllESignaturePreference,
  useGetESignaturePreferenceFileByFileId,
  useUpdateESignaturePreference
} from '../../../../api/ApiQueries'
import html2canvas from 'html2canvas'
import pen from '../../../../svg/Workflow builder/Sign.svg'
import { ObjectLinkDropdown } from '../../../UI/AsyncDropdowns/ObjectLinkDropdown/ObjectLinkDropdown'
import { useApi } from '../../../../hooks/useApi'
import { max, result, set } from 'lodash'
import { Checkbox } from 'primereact/checkbox'
import { Checkbox as MCheckbox } from '../../../UI/Input/CheckBox/CheckBox'
import TextInput from '../../../UI/Input/TextInput/TextInput'
// import DetailIcon from "../../svg/Main Dashboard/Detail.svg";
// import eSignIcon from "../../svg/Main Dashboard/Epreference.svg";
// import GroupIcon from "../../svg/Main Dashboard/Group_manager.svg";
// import eye from "../../svg/Main Dashboard/edit_sub.svg";

const dataverseApi = process.env.NEXT_PUBLIC_DATAVERSE_API
const formBuilderStudioApi = process.env.NEXT_PUBLIC_FORM_BUILDER_API

// Ahmet: Maybe we could define a structure for different types of inputs
// This will make understand what input is looking like
// but some components has dynamic sructure like autocomplete, table etc...
export const generateMask = (
  isRichText = false,
  isCheckboxOrMultiSelect = false,
  isSignature = false,
  isAddress = false,
  isAdvancedFileUpload = false,
  isVersionedFileUpload = false,
  componentProps = {}
) => {
  if (isRichText) {
    return JSON.stringify({
      root: {
        children: [
          {
            children: [
              {
                detail: 0,
                format: 0,
                mode: 'normal',
                style: '',
                text: '●●●●●●●●●',
                type: 'text',
                version: 1
              }
            ],
            direction: 'ltr',
            format: '',
            indent: 0,
            type: 'paragraph',
            version: 1
          }
        ],
        direction: 'ltr',
        format: '',
        indent: 0,
        type: 'root',
        version: 1
      }
    })
  } else if (isCheckboxOrMultiSelect) {
    return []
  } else if (isSignature) {
    return { value: '●●●●●●●●' }
  } else if (isAddress) {
    return {
      addressObj: {
        street_line: '●●●●●●●●',
        city: '●●●●●●●●',
        state: '●●●●●●●●',
        zipcode: '●●●●●●●●'
      }
    }
  } else if (isAdvancedFileUpload || isVersionedFileUpload) {
    if (!componentProps || typeof componentProps !== 'object') {
      return {}
    }
    const temp = JSON.parse(JSON.stringify(componentProps))
    Object.keys(temp).map((prop) => {
      temp[prop].fileType = '●●●●●●●●'
      temp[prop].fileName = '●●●●●●●●'
    })

    return temp
  } else {
    return '●●●●●●●●'
  }
}

export function ViewComponent({
  guid,
  metadata,
  setMetadata,
  metadataWithPermissions,
  inputs,
  handleInputChange,
  assignValuesNested,
  errors,
  checkErrors,
  formSubmission,
  formSubmissionData,
  disabled,
  currentPage,
  setCurrentPage,
  isSubmissionPage,
  files,
  nestedAssignMetadata,
  setInputs,
  pageData,
  objectKeysArray,
  isApprovalPage,
  accordionId,
  status
}) {
  const { name, type, divClassName, permission } = metadataWithPermissions[guid]
  const isPageBreak = type === 'pageBreak'
  const isRichText = type === 'richText'
  const isCheckboxOrMultiSelect = type === 'checkbox' || type === 'multiselect'
  const isSignature = type === 'signature'
  const isAddress = type === 'address'
  const isAdvancedFileUpload = type === 'advancedFileUpload'
  const isVersionedFileUpload = type === 'versionedFileUpload'
  let currentMetadata = metadataWithPermissions[guid]

  const viewComponentsObj = {
    metadata: currentMetadata,
    setMetadata: setMetadata,
    value:
      permission === 'Display in Masked' && !isSubmissionPage
        ? generateMask(
            isRichText,
            isCheckboxOrMultiSelect,
            isSignature,
            isAddress,
            isAdvancedFileUpload,
            isVersionedFileUpload,
            inputs?.[name] ?? null
          )
        : inputs?.[name] ?? null,
    onChange: handleInputChange,
    errors: errors[name],
    wholeErrors: errors,
    paginationErrors: isPageBreak ? errors : null,
    checkErrors: isPageBreak ? checkErrors : null,
    assignValuesNested: assignValuesNested,
    componentDisabled: disabled ? disabled() : null,
    formSubmission,
    formSubmissionData,
    currentPage,
    setCurrentPage,
    objectKeysArray,
    isSubmissionPage,
    isApprovalPage,
    pageData,
    inputs,
    files,
    nestedAssignMetadata,
    setInputs,
    wholeMetadata: metadata,
    metadataWithPermissions,
    accordionId,
    status
  }

  return (
    <ViewComponentContainer
      key={guid}
      permission={permission}
      isSubmissionPage={isSubmissionPage}
      // divClassName={isApprovalPage ? "col-5 mb-3" : divClassName}
      divClassName={divClassName}
      viewContainerStyle={viewStyles.viewComponentContainer}
    >
      {createElement(componentMapper[type], viewComponentsObj)}
    </ViewComponentContainer>
  )
}

export default function ViewComponents({
  metadata,
  setMetadata,
  inputs,
  handleInputChange,
  assignValuesNested,
  errors,
  checkErrors,
  formSubmission,
  formSubmissionData,
  disabled,
  currentPage,
  setCurrentPage,
  isSubmissionPage = false,
  files,
  nestedAssignMetadata,
  setInputs,
  pageData,
  objectKeysArray,
  metadataWithPermissions,
  key,
  accordionId = null,
  status,
  isApprovalPage = false
}) {
  const pageFields = Object.keys(pageData).reduce(
    (prev, curr) => {
      const field = pageData[curr]
      if (field.parentId) {
        const key = `${field.parentId}$${field.accordionIndex}`
        prev.childFields[key] = {
          ...prev.childFields[key],
          [curr]: field
        }
      } else {
        prev.parentFields[curr] = field
      }
      return prev
    },
    {
      parentFields: {},
      childFields: {}
    }
  )

  console.log('pageFields', pageFields, accordionId, pageFields.childFields[accordionId])

  return (
    <div key={key} className={viewStyles.containerLayout}>
      {pageData &&
        Object.keys(accordionId ? pageFields.childFields[accordionId] ?? {} : pageFields.parentFields ?? {}).map((guid) => {
          console.log('guid', guid, componentMapper[metadata[guid]?.type], metadata[guid]?.type)
          if (!componentMapper[metadata[guid]?.type]) {
            return null
          }

          return (
            <ViewComponent
              key={guid}
              guid={guid}
              metadata={metadata}
              metadataWithPermissions={metadataWithPermissions}
              inputs={inputs}
              handleInputChange={handleInputChange}
              assignValuesNested={assignValuesNested}
              errors={errors}
              checkErrors={checkErrors}
              formSubmission={formSubmission}
              formSubmissionData={formSubmissionData}
              disabled={disabled}
              currentPage={currentPage}
              setCurrentPage={setCurrentPage}
              isSubmissionPage={isSubmissionPage}
              files={files}
              nestedAssignMetadata={nestedAssignMetadata}
              setInputs={setInputs}
              pageData={pageData}
              objectKeysArray={objectKeysArray}
              isApprovalPage={isApprovalPage}
              accordionId={accordionId}
              status={status}
            />
          )
        })}
    </div>
  )
}

export function ViewHeader({ metadata }) {
  const { label, width, height, file, alignment } = metadata
  const [image, setImage] = useState()

  useEffect(() => {
    const fetchImage = async () => {
      try {
        const apiUrl = `${formBuilderStudioApi}FormMetadata/file/${file.guid}` // Update this URL to the actual API endpoint
        const response = await fetch(apiUrl)

        if (!response.ok) {
          throw new Error(`HTTP error! Status: ${response.status}`)
        }

        const blob = await response.blob()
        const reader = new FileReader()

        reader.onloadend = () => {
          setImage(reader.result)
        }

        reader.readAsDataURL(blob)
      } catch (error) {
        console.error('Error fetching image:', error)
      }
    }

    if (file) {
      fetchImage()
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])

  return (
    <>
      <ViewComponentContainer className={clsx(viewStyles.viewHeaderContainer, 'col-12')}>
        <AlignmentContainer alignment={alignment} isHeader={true}>
          <div className="col-4">
            {image && (
              <div className={headerAndImageStyles.imageWrapper} style={{ width: width, height: height }}>
                <NextImage src={image} alt="Uploaded" fill />
                {/* <img src={image} alt="Uploaded" fill /> */}
              </div>
            )}
          </div>
          <div className="col-8">
            <div className={viewStyles.widthTwoThirds}>
              <ReadonlyLexicalEditor value={label} />
            </div>
          </div>
        </AlignmentContainer>
      </ViewComponentContainer>
    </>
  )
}

export function ViewText({ metadata, value, onChange, errors, componentDisabled }) {
  const { name, label, subtitle, disabled, alignment, validations, permission } = metadata
  return (
    <ComponentContainer permission={permission}>
      <AlignmentContainer alignment={alignment}>
        <LabelContainer alignment={alignment}>
          <Label label={label} validations={validations} />
        </LabelContainer>
        <InputsContainer>
          <InputText
            className={clsx(viewStyles.maxWidth, textStyles.inputText, errors?.length > 0 && 'p-invalid')}
            name={name}
            value={value}
            onChange={onChange}
            disabled={componentDisabled || disabled}
          />
          <Subtitle subtitle={subtitle} />
          <Errors errors={errors} />
        </InputsContainer>
      </AlignmentContainer>
    </ComponentContainer>
  )
}

export function ViewCalendar({ metadata, value, onChange, errors, componentDisabled }) {
  const {
    name,
    label,
    subtitle,
    validations,
    disabled,
    alignment,
    permission,
    isDefaultValueDynamic,
    isPastDateRestricted,
    isFutureDateRestricted
  } = metadata

  const getValue = () => {
    if (isDefaultValueDynamic) {
      return new Date()
    } else {
      return value ? new Date(value) : null
    }
  }

  return (
    <ComponentContainer>
      <AlignmentContainer alignment={alignment}>
        <LabelContainer alignment={alignment}>
          <Label label={label} validations={validations} />
        </LabelContainer>
        <InputsContainer>
          {permission && permission === 'Display in Masked' ? (
            <MaskPermissionInput />
          ) : (
            <Calendar
              className={clsx(viewStyles.maxWidth, calendarStyles.calendar, errors?.length > 0 && 'p-invalid')}
              name={name}
              value={getValue()}
              onChange={onChange}
              disabled={disabled || componentDisabled || isDefaultValueDynamic}
              minDate={isPastDateRestricted ? new Date() : null}
              maxDate={isFutureDateRestricted ? new Date() : null}
            />
          )}
          <Subtitle subtitle={subtitle} />
          <Errors errors={errors} />
        </InputsContainer>
      </AlignmentContainer>
    </ComponentContainer>
  )
}

export function ViewTime({ metadata, value, onChange, errors, componentDisabled }) {
  const { name, disabled, label, subtitle, validations, alignment, permission } = metadata

  return (
    <ComponentContainer>
      <AlignmentContainer alignment={alignment}>
        <LabelContainer alignment={alignment}>
          <Label label={label} validations={validations} />
        </LabelContainer>
        <InputsContainer>
          {permission && permission === 'Display in Masked' ? (
            <MaskPermissionInput />
          ) : (
            <Calendar
              className={clsx(viewStyles.maxWidth, calendarStyles.calendar, errors?.length > 0 && 'p-invalid')}
              timeOnly
              showTime
              hourFormat="12"
              name={name}
              value={value ? new Date(value) : null}
              onChange={onChange}
              disabled={disabled || componentDisabled}
            />
          )}
          <Subtitle subtitle={subtitle} />
          <Errors errors={errors} />
        </InputsContainer>
      </AlignmentContainer>
    </ComponentContainer>
  )
}

export function ViewNumber({ metadata, value, onChange, errors, componentDisabled, invalidStyle }) {
  const { name, label, subtitle, disabled, validations, numberFormat, alignment, permission } = metadata

  const formatObj = {
    ...(numberFormat === 'decimal' && {
      maxFractionDigits: 2,
      minFractionDigits: 2
    }),
    ...(numberFormat === 'both' && { maxFractionDigits: 2 })
  }

  return (
    <ComponentContainer>
      <AlignmentContainer alignment={alignment}>
        <LabelContainer alignment={alignment}>
          <Label label={label} validations={validations} />
        </LabelContainer>
        <InputsContainer>
          {permission && permission === 'Display in Masked' ? (
            <MaskPermissionInput />
          ) : (
            <InputNumber
              className={clsx(viewStyles.maxWidth, numberStyles.number, errors?.length > 0 && 'p-invalid')}
              name={name}
              value={value}
              onChange={onChange}
              // maxFractionDigits={2}
              useGrouping={false}
              disabled={disabled || componentDisabled}
              {...formatObj}
            />
          )}
          <Subtitle subtitle={subtitle} />
          <Errors errors={errors} />
        </InputsContainer>
      </AlignmentContainer>
    </ComponentContainer>
  )
}

export function ViewTextarea({ metadata, value, onChange, errors, componentDisabled }) {
  const { name, label, subtitle, disabled, alignment, validations } = metadata

  return (
    <ComponentContainer>
      <AlignmentContainer alignment={alignment}>
        <LabelContainer
          // className={`${textareaStyles.textareaLabel} ${textareaStyles.labelContainer} ${viewStyles.topSpacingLarge}`}
          className={clsx(
            `${textareaStyles.textareaLabelWidth} ${textareaStyles.textareaLabel} ${viewStyles.topSpacingLarge}`, // ${textareaStyles.labelContainer}
            alignment === 'column' && textareaStyles.topAligned,
            alignment === 'row-reverse' && textareaStyles.rightAligned
          )}
          alignment={alignment}
        >
          <Label label={label} validations={validations} />
        </LabelContainer>
        <InputsContainer className={textareaStyles.inputsContainer} style={{ width: '100%', padding: '0 0.5rem' }}>
          <InputTextarea
            rows={1}
            className={clsx(
              // viewStyles.maxWidth,
              // textareaStyles.textareaInput,
              errors?.length > 0 && 'p-invalid'
            )}
            name={name}
            value={value}
            autoResize
            onChange={onChange}
            disabled={disabled || componentDisabled}
          />
          <Subtitle subtitle={subtitle} />
          <Errors errors={errors} />
        </InputsContainer>
      </AlignmentContainer>
    </ComponentContainer>
  )
}

export function ViewMask({ metadata, value, onChange, errors, componentDisabled }) {
  const { name, label, subtitle, disabled, mask, alignment, validations, permission } = metadata

  return (
    <ComponentContainer>
      <AlignmentContainer alignment={alignment}>
        <LabelContainer alignment={alignment}>
          <Label label={label} validations={validations} />
        </LabelContainer>
        <InputsContainer>
          {permission && permission === 'Display in Masked' ? (
            <MaskPermissionInput />
          ) : (
            <InputMask
              className={clsx(viewStyles.maxWidth, maskStyles.mask, errors?.length > 0 && 'p-invalid')}
              name={name}
              value={value}
              onChange={onChange}
              mask={mask}
              disabled={disabled || componentDisabled}
            />
          )}
          <Subtitle subtitle={subtitle} />
          <Errors errors={errors} />
        </InputsContainer>
      </AlignmentContainer>
    </ComponentContainer>
  )
}

export function ViewDropdown({ metadata, value, onChange, errors, componentDisabled }) {
  const { name, label, subtitle, disabled, options, validations, alignment, sortingOrder, isStickyEnabled, stickyOption } = metadata
  const { sortOptions } = useDropdownSorting()

  return (
    <ComponentContainer>
      <AlignmentContainer alignment={alignment}>
        <LabelContainer alignment={alignment}>
          <Label label={label} validations={validations} />
        </LabelContainer>
        <InputsContainer>
          <Dropdown
            name={name}
            className={clsx(viewStyles.maxWidth, dropdownStyles.dropdown, errors?.length > 0 && 'p-invalid')}
            value={value}
            onChange={onChange}
            options={sortOptions(options, sortingOrder, isStickyEnabled, stickyOption)}
            disabled={disabled || componentDisabled}
          />
          <Subtitle subtitle={subtitle} />
          <Errors errors={errors} />
        </InputsContainer>
      </AlignmentContainer>
    </ComponentContainer>
  )
}

export function ViewMultiselect({ metadata, value, onChange, errors, componentDisabled }) {
  const { name, label, subtitle, disabled, options, validations, alignment } = metadata

  return (
    <ComponentContainer>
      <AlignmentContainer alignment={alignment}>
        <LabelContainer alignment={alignment}>
          <Label label={label} validations={validations} />
        </LabelContainer>
        <InputsContainer>
          <MultiSelect
            className={clsx(viewStyles.maxWidth, multiselectStyles.multiselect, errors?.length > 0 && 'p-invalid')}
            name={name}
            value={value}
            onChange={onChange}
            options={options}
            display="chip"
            disabled={disabled || componentDisabled}
          />
          <Subtitle subtitle={subtitle} />
          <Errors errors={errors} />
        </InputsContainer>
      </AlignmentContainer>
    </ComponentContainer>
  )
}

export function ViewImage({ metadata }) {
  const { name, label, subtitle, width, height, file, alignment } = metadata
  const [image, setImage] = useState()

  useEffect(() => {
    const fetchImage = async () => {
      try {
        const apiUrl = `${formBuilderStudioApi}FormMetadata/file/${file.guid}` // Update this URL to the actual API endpoint
        const response = await fetch(apiUrl)

        if (!response.ok) {
          throw new Error(`HTTP error! Status: ${response.status}`)
        }

        const blob = await response.blob()
        const reader = new FileReader()

        reader.onloadend = () => {
          setImage(reader.result)
        }

        reader.readAsDataURL(blob)
      } catch (error) {
        console.error('Error fetching image:', error)
      }
    }

    if (file) {
      fetchImage()
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])

  return (
    <>
      <ComponentContainer className={viewStyles.imageComponentContainer}>
        {/* <div style={{flexDirection:'column'}}> */}
        <AlignmentContainer alignment={alignment} isImage={true} style={{ flexDirection: 'column', display: 'flex', width: '100%' }}>
          <LabelContainer
            className={`${viewStyles.topSpacing} ${viewStyles.imageLabelContainer}`}
            alignment={alignment}
            style={{ marginLeft: alignment === 'row-reverse' ? '0.5rem' : 0 }}
          >
            <Label label={label} />
          </LabelContainer>
          <InputsContainer className={`${viewStyles.inputsContainer} ${viewStyles.topSpacingLarge}`}>
            {image && (
              <div className={viewStyles.imageWrapper} style={{ width: width, height: height }}>
                <NextImage src={image} alt="Uploaded" fill />
              </div>
            )}
            <Subtitle subtitle={subtitle} />
          </InputsContainer>
        </AlignmentContainer>
        {/* </div> */}
      </ComponentContainer>
    </>
  )
}

export function ViewFileInput({ metadata, value, onChange, errors, files, isSubmissionPage, componentDisabled }) {
  const { name, guid, label, subtitle, disabled, multiple, validations, alignment, permission } = metadata

  const truncateFileName = (fileName) => {
    return fileName.length > 50 ? `${fileName.substring(0, 40)}...` : fileName
  }

  const openFileInNewTab = (file) => {
    try {
      const fileURL = file instanceof File ? URL.createObjectURL(file) : file.name

      const newWindow = window.open(fileURL, '_blank')

      if (newWindow === null) {
        alert('Please allow popups for this website')
        return
      }

      if (file instanceof File) {
        setTimeout(() => {
          URL.revokeObjectURL(fileURL)
        }, 100)
      }
    } catch (error) {
      console.error('Error opening file:', error)
      alert('Error opening file. Please try again.')
    }
  }

  const getMergedFilesAndValuesToDisplay = (files, values) => {
    if (!Array.isArray(files) || !Array.isArray(values)) {
      return []
    }

    return files.concat(values)
  }

  const mergedFilesAndValues = getMergedFilesAndValuesToDisplay(files?.[name] ?? [], value ?? [])

  return (
    <ComponentContainer>
      <AlignmentContainer alignment={alignment}>
        <LabelContainer
          // className={fileStyles.labelContainer}
          className={clsx(
            fileStyles.labelContainer,
            alignment === 'column' && fileStyles.topAligned,
            alignment === 'row-reverse' && fileStyles.rightAligned
          )}
          alignment={alignment}
        >
          <Label label={label} validations={validations} />
        </LabelContainer>
        <InputsContainer className={fileStyles.fileInputContainerMobile}>
          <UrlFileInput
            name={name}
            className={clsx(fileStyles.fileWidth, fileStyles.file)}
            onChange={onChange}
            multiple={multiple}
            errors={errors}
            disabled={disabled || componentDisabled}
            isApproverPage={!isSubmissionPage}
            files={files}
            guid={guid}
            value={value}
            buttonText={
              files && files[`file_${guid}`] && files[`file_${guid}`].length > 0
                ? `File Names: ${files[`file_${guid}`].map((file) => truncateFileName(file.name)).join(', ')}`
                : 'Choose a File'
            }
          />
          <Subtitle subtitle={subtitle} />
          <Errors errors={errors} />
          {isSubmissionPage &&
            mergedFilesAndValues.map((file, index) => (
              <div className={`${viewStyles.fileDownloadWrapper}`} key={index + file.name} onClick={() => openFileInNewTab(file)}>
                <i className={`pi pi-file ${viewStyles.fileDownloadIcon}`} />
                <label className={`${viewStyles.fileDownloadLabel}`}>{file.name}</label>
              </div>
            ))}
          {!isSubmissionPage && mergedFilesAndValues.map((file, index) => <FileDownload key={index + file.name} file={file} />)}
        </InputsContainer>
      </AlignmentContainer>
    </ComponentContainer>
  )
}

const FileDownload = ({ file }) => {
  const downloadFile = async (file) => {
    if (!file.guid) {
      window.open(file.name, '_blank', 'noopener,noreferrer')
      return
    }

    try {
      const apiUrl = `${process.env.NEXT_PUBLIC_FORM_BUILDER_API}FormMetadata/file/${encodeURIComponent(file.guid)}`
      const response = await fetch(apiUrl)

      if (response.ok) {
        const blob = await response.blob()
        saveAs(blob, `${file?.fileName}`)
      } else {
        console.error(`Error while downloading the file: ${response}`)
      }
    } catch (error) {
      console.error(`Error while downloading the file: ${error.message}`)
    }
  }

  return (
    <div className={viewStyles.fileDownloadWrapper} onClick={() => downloadFile(file)}>
      <i className={`pi pi-file ${viewStyles.fileDownloadIcon}`} />
      <label className={viewStyles.fileDownloadLabel}>{file?.fileName ?? file?.name}</label>
    </div>
  )
}

export function ViewRichText({ metadata, value, assignValuesNested, errors, componentDisabled }) {
  const { name, label, subtitle, disabled, alignment, validations } = metadata
  const [readOnly, setReadOnly] = useState(disabled || componentDisabled)

  useEffect(() => {
    setReadOnly(disabled || componentDisabled)
  }, [disabled, componentDisabled])

  return (
    <ComponentContainer>
      <AlignmentContainer alignment={alignment}>
        <LabelContainer
          className={clsx(
            `${textareaStyles.textareaLabel} ${viewStyles.textareaLabelWidthContainer}`,
            alignment === 'column' && textareaStyles.topAligned,
            alignment === 'row-reverse' && textareaStyles.rightAligned
          )}
          alignment={alignment}
        >
          <Label label={label} validations={validations} />
        </LabelContainer>
        <InputsContainer className={viewStyles.richTextInputsContainer}>
          <div
            style={{
              border: errors && errors.length > 0 ? '1px solid red' : null
            }}
          >
            <LexicalEditor name={name} value={value} onChange={assignValuesNested} readOnly={readOnly} />
          </div>
          <Subtitle subtitle={subtitle} />
          <Errors errors={errors} />
        </InputsContainer>
      </AlignmentContainer>
    </ComponentContainer>
  )
}

export function ViewReadonlySubtitle({ metadata }) {
  const { subtitle } = metadata
  return (
    <div className={viewStyles.subtitleMargin}>
      <ReadonlyLexicalEditor value={subtitle} />
    </div>
  )
}

export function ViewSignature({ metadata, value, onChange, errors, formSubmission, formSubmissionData, isSubmissionPage, status }) {
  const userProfile = useContext(UserProfileContext)
  const { name, label, subtitle, disabled, selectedFontStyle, alignment, validations } = metadata
  const router = useRouter()
  const { accounts } = useMsal()
  const account = useAccount(accounts[0] ?? {})
  const [show, setShow] = useState(false)
  const [activeIndex, setActiveIndex] = useState(0)
  const [selectedFont, setSelectedFont] = useState("'Sassy Frass', cursive")
  const [signatureStyle, setsignatureStyle] = useState('signatureStyle1')
  const [isCardVisible, setIsCardVisible] = useState(false)
  const firstName = userProfile?.displayName ? userProfile?.displayName.split(' ')[0] : ''
  const lastName = userProfile?.displayName ? userProfile?.displayName.split(' ')[1] : ''
  const [configValue, setConfigValue] = useState({
    legalName: firstName,
    initial: lastName ? lastName[0] : ''
  })
  const [initialUploadedFile, setInitialUploadedFile] = useState(null)
  const [signedUploadedFile, setSignedUploadedFile] = useState(null)
  // const [edit, setEdit] = useState(isSubmissionPage ? `${userProfile?.firstName} ${userProfile?.lastName}` !== value?.value : value?.value !== value?.fullLegalName)
  const [display, setDisplay] = useState(isSubmissionPage ? false : value?.siged && value?.selectedSign)
  // const [edit, setEdit] = useState(false)

  const sigCanvasRef = useRef(null)
  const initialCanvasRef = useRef(null)

  useEffect(() => {
    setTimeout(() => {
      userProfile.Refetch()
    }, 500)
  }, [show])

  const fileUploaderSignature = useRef(null)
  const toastBottomCenter = useRef(null)
  const handleFileUploadSignature = (e) => {
    const uploadedFile = e.target.files[0]
    if (uploadedFile.size > 5000000) {
      toastBottomCenter.current.show({
        severity: 'error',
        summary: 'Error',
        detail: 'File size exeeds the maximum limit',
        life: 2500
      })
      return
    }
    const blob = new Blob([uploadedFile], { type: uploadedFile.type })
    var reader = new FileReader()
    reader.readAsDataURL(blob)
    reader.onloadend = function () {
      var base64data = reader.result
      setSignedUploadedFile(base64data)
    }
  }
  const fileUploaderInitial = useRef(null)
  const handleFileUploadInitial = (e) => {
    const uploadedFile = e.target.files[0]
    if (uploadedFile.size > 5000000) {
      toastBottomCenter.current.show({
        severity: 'error',
        summary: 'Error',
        detail: 'File size exeeds the maximum limit',
        life: 2500
      })
      return
    }
    const blob = new Blob([uploadedFile], { type: uploadedFile.type })
    var reader = new FileReader()
    reader.readAsDataURL(blob)
    reader.onloadend = function () {
      var base64data = reader.result
      setInitialUploadedFile(base64data)
    }
  }
  const handleDragOver = (e) => {
    e.preventDefault()
  }

  const handleDropSignature = (e) => {
    e.preventDefault()
    const file = e.dataTransfer.files[0]
    setSignedUploadedFile(URL.createObjectURL(file))
  }

  const handleDropInitial = (e) => {
    e.preventDefault()
    const file = e.dataTransfer.files[0]
    setInitialUploadedFile(URL.createObjectURL(file))
  }

  const handleGetAllSignPref = (allSignaturePref) => {
    setSelectedCategory(
      allSignaturePref?.type !== undefined && allSignaturePref?.type !== null
        ? categories.find((item) => item.key === allSignaturePref?.type)
        : categories[0]
    )

    if (allSignaturePref.drawSignFileId) {
      drawSignFileIdMutate({
        fileId: allSignaturePref.drawSignFileId
      })
    }
    if (allSignaturePref.drawIntialFileId) {
      drawIntialFileIdMutate({
        fileId: allSignaturePref.drawIntialFileId
      })
    }
    if (allSignaturePref.caligraphicSignFileId) {
      caligraphicSignFileIdMutate({
        fileId: allSignaturePref.caligraphicSignFileId
      })
    }
    if (allSignaturePref.caligraphicIntialFileId) {
      caligraphicIntialFileIdMutate({
        fileId: allSignaturePref.caligraphicIntialFileId
      })
    }

    if (allSignaturePref.uploadSignFileId) {
      uploadSignFileIdMutate({
        fileId: allSignaturePref.uploadSignFileId
      })
    }

    if (allSignaturePref.uploadIntialFileId) {
      uploadIntialFileIdMutate({
        fileId: allSignaturePref.uploadIntialFileId
      })
    }
  }

  const { data: getAllESignaturePrefData, mutate: getAllESignaturePrefMutate } = useGetAllESignaturePreference(handleGetAllSignPref)

  const handlePostOnsettle = (res) => {
    if (userProfile.userID) {
      getAllESignaturePrefMutate({ id: userProfile.userID })
    }
    return res
  }

  const {
    mutate: postESignPrefMutate,
    data: postESignPrefData,
    isLoading: postESignPrefIsLoading
  } = usePostESignaturePreference(handlePostOnsettle, (data, x, y) => setShow(false))

  if (metadata.validations) {
    metadata.validations.signatureName = router.pathname.includes('/form-data/') ? formSubmission.userFullLegalName : account.name
  } else {
    metadata.validations = {}
    metadata.validations.signatureName = router.pathname.includes('/form-data/') ? formSubmission.userFullLegalName : account.name
  }

  const onSignatureChange = (e) => {
    const signTime = new Date()
    const signProperties = {
      signatureGuid: '',
      signature: userProfile?.selectedSign,
      signTime: `${signTime}`,
      userId: `${userProfile?.userID}`,
      userGuid: userProfile?.adObjectID,
      firstName: userProfile?.firstName,
      lastName: userProfile?.lastName,
      fullLegalName: account.name,
      email: account.username,
      securityLevel: 'Email, Account Authentication(None)',
      userIPv4: '',
      signatureAdoption: 'Pre-Selected Style',
      value: '',
      signed: true
    }
    onChange({
      target: {
        name: name,
        value: signProperties
      }
    })

    setDisplay(signProperties?.localAccountId == userProfile.localAccountId)
  }

  const getSignatureImage = (ref) => {
    if (ref.current) {
      const canvas = ref.current.getTrimmedCanvas()
      const imageDataUrl = canvas.toDataURL('image/png') // or 'image/jpeg'
      return imageDataUrl
    }
    return null
  }

  const base64ToFile = (base64, mimeType, fileName) => {
    // Remove the data URL prefix if present
    const base64String = base64?.replace(/^data:image\/[a-zA-Z]+;base64,/, '')

    // Decode the base64 string to a byte string
    let byteString
    try {
      byteString = atob(base64String)
    } catch (e) {
      console.error('Failed to decode base64 string:', e)
      return null // or handle the error as needed
    }

    // Create an array to hold the byte values
    let arrayBuffer = new ArrayBuffer(byteString?.length)
    let uint8Array = new Uint8Array(arrayBuffer)

    // Fill the array with byte values
    for (let i = 0; i < byteString.length; i++) {
      uint8Array[i] = byteString.charCodeAt(i)
    }

    // Create and return a File from the byte array
    return new File([uint8Array], fileName, { type: mimeType })
  }

  function changeHander(e) {
    const { name, value } = e.target
    setConfigValue((prev) => ({ ...prev, [name]: value }))
  }

  const captureAndProcessElement = async (elementId) => {
    const element = document.getElementById(elementId)
    const canvas = await html2canvas(element)
    const dataUrl = canvas.toDataURL('image/png')
    return dataUrl.replace('data:image/png;base64,', '')
  }

  const handlePredefinedImage = async () => {
    const signData = await captureAndProcessElement(`SignatureType`)
    const initialData = await captureAndProcessElement(`InitialType`)

    const formData = createFormData(signData, initialData)

    if (!userProfile.selectedSign) {
      postESignPrefMutate({ body: formData })
    }
  }

  const createFormData = (signData, initialData) => {
    const formData = new FormData()
    formData.append('UserId', userProfile.userID)
    formData.append('Type', ESignPreferenceType.Caligraphic)
    if (signData) {
      formData.append('CaligraphicSign', base64ToFile(signData, 'image/png', 'DrawSign.png'))
    }
    if (initialData) {
      formData.append('CaligraphicIntial', base64ToFile(initialData, 'image/png', 'DrawInitial.png'))
    }
    return formData
  }

  const handleStyleChangeClick = () => {
    setIsCardVisible(!isCardVisible)
  }

  const handleFontFamilyChange = (selectedFont) => {
    setsignatureStyle(selectedFont.value)
    setSelectedFont(selectedFont)
    setIsCardVisible(!isCardVisible)
    // You can add additional logic here if needed
  }

  const fontOptions = [
    { value: "'Sassy Frass', cursive", label: 'Sassy Frass' },
    { value: "'Parisienne', cursive", label: 'Parisienne' },
    { value: "'Italianno', cursive", label: 'Italianno' },
    { value: "'Mrs Saint Delafield', cursive", label: 'Mrs Saint Delafield' },
    { value: "'Rouge Script', cursive", label: 'Rouge Script' }
    // Add more font options as needed
  ]

  return (
    <>
      <ComponentContainer>
        <AlignmentContainer alignment={alignment}>
          <div className="flex justify-content-start gap-2 align-items-center" style={{ padding: '0.5rem 0.5rem 0.5rem 0' }}>
            <LabelContainer alignment={alignment}>
              <span style={{ width: '100%' }}>
                <Label label={label} validations={validations} />
                {isSubmissionPage && formSubmission?.status === 11 && (value?.signed ?? false) ? (
                  <span className="pi pi-pencil ml-3" style={{ cursor: 'pointer' }} onClick={() => setShow(true)} />
                ) : null}
              </span>
              {!userProfile.selectedSign && !value?.signature ? (
                <Button
                  className={style.secondaryButton}
                  disabled={!!disabled}
                  style={{ width: '330px' }}
                  label={'+ Create eSignature'}
                  onClick={() => setShow(true)}
                />
              ) : !value?.signed ? (
                <Button
                  className={style.secondaryButton}
                  disabled={!!disabled}
                  style={{ width: '330px' }}
                  label={'Import eSignature'}
                  onClick={() => {
                    onSignatureChange()
                  }}
                />
              ) : null}
            </LabelContainer>
            {/* {
              isSubmissionPage
                ?
                (!disabled && !userProfile.selectedSign) &&
                <Button className={style.addButton} style={{ width: "auto" }} label={"+ Create eSignature"} onClick={() => setShow(true)} />
                :
                disabled ? null : !userProfile.selectedSign ? <Button className={style.addButton} style={{ width: "auto" }} label={"+ Create eSignature"} onClick={() => setShow(true)} /> : null
            } */}
            {/* {
              (
                isSubmissionPage ? (!disabled && !edit && `${userProfile?.firstName} ${userProfile?.lastName}` === value?.value) && <span className="pi pi-pencil" style={{ cursor: 'pointer' }} onClick={() => setEdit(true)} />
                  :
                  disabled ? null : (!edit && `${userProfile?.firstName} ${userProfile?.lastName}` === value?.value) ? <span className="pi pi-pencil" style={{ cursor: 'pointer' }} onClick={() => setEdit(true)} /> : null
              )
            } */}
          </div>
          <InputsContainer>
            {isSubmissionPage ? (
              value?.signed && value?.signature ? (
                <Image src={value?.signature ? `data:image/png;base64, ${value?.signature}` : ''} width={200} height={50} />
              ) : null
            ) : (
              value?.signature && (
                <Image src={value?.signature ? `data:image/png;base64, ${value?.signature}` : ''} width={200} height={50} />
              )
            )}
            {/* {
              (isSubmissionPage ? (!disabled && !edit && `${userProfile?.firstName} ${userProfile?.lastName}` === value?.value) : (value && ((value?.signature) || disabled) && !edit)) ?
                <>
                  <Image src={value?.signature ? `data:image/png;base64, ${value?.signature}` : ""} width={200} height={50} />
                </>
                :
                <>
                  <Button className={style.addButton} style={{ width: "auto" }} label={"Import eSignature"} onClick={() => {
                    
                  }} />
                  <InputText
                    className={clsx(
                      viewStyles.maxWidth,
                      signatureStyles.signature,
                      errors?.length > 0 && "p-invalid"
                    )}
                    name={name}
                    value={value?.value}
                    onChange={(e) => onSignatureChange(e)}
                    style={{ fontFamily: selectedFontStyle }}
                    disabled={(disabled || !userProfile.selectedSign)}
                  />
                  <Subtitle subtitle={subtitle} />
                  <Errors errors={errors} />
                  {router.pathname.includes("/form-data/") && (
                    <DisplaySignatureInfo
                      formSubmission={formSubmission}
                      signatureData={formSubmissionData[name]}
                    />
                  )}
                </>
            } */}
          </InputsContainer>
        </AlignmentContainer>
      </ComponentContainer>
      <Modal header={'Create Signature'} visible={show} onHide={() => setShow(false)} style={{ width: '60vw' }}>
        <div className={style.tabRow}>
          <Button className={activeIndex === 0 ? style.activeTab : style.tabPart} onClick={() => setActiveIndex(0)}>
            Draw
          </Button>
          <Button className={activeIndex === 1 ? style.activeTab : style.tabPart} onClick={() => setActiveIndex(1)}>
            Type
          </Button>
          <Button className={activeIndex === 2 ? style.activeTab : style.tabPart} onClick={() => setActiveIndex(2)}>
            Upload
          </Button>
        </div>
        <div className={style.scroll}>
          <TabView
            activeIndex={activeIndex}
            onTabChange={(e) => {
              setActiveIndex(e.index)
            }}
          >
            <TabPanel headerTemplate={<></>}>
              <div className={style.tabHead}>Draw your signature</div>

              <div className={style.headDraw}>
                <div className={clsx('flex align-items-center flex-column', style.uploadDraw)} style={{ width: '60%' }}>
                  <SignatureCanvas
                    penColor="black"
                    canvasProps={{
                      className: 'sigCanvas',
                      width: '450'
                    }}
                    ref={sigCanvasRef}
                  />
                  <span
                    className={style.clearDraw}
                    onClick={() => {
                      sigCanvasRef.current.clear()
                    }}
                  >
                    {'Clear'}
                  </span>
                </div>

                <div className={clsx('flex align-items-center flex-column', style.uploadDraw)} style={{ width: '40%' }}>
                  <SignatureCanvas
                    penColor="black"
                    canvasProps={{
                      className: 'sigCanvas'
                    }}
                    ref={initialCanvasRef}
                  />
                  <span
                    className={style.clearInit}
                    onClick={() => {
                      initialCanvasRef.current.clear()
                    }}
                  >
                    {'Clear'}
                  </span>
                </div>
              </div>
            </TabPanel>
            <TabPanel headerTemplate={<></>}>
              <div className={style.headerTemplate}>
                <div style={{ width: '60%' }}>
                  <label className={style.headerText}>Full Name</label>
                  <Image src={star} alt="Image" />
                  <InputText
                    name="legalName"
                    className={style.inputText}
                    value={configValue.legalName}
                    placeholder="Enter your full name"
                    disabled={true}
                  />
                </div>

                <div style={{ width: '40%' }}>
                  <label className={style.headerText}>Initials</label>
                  <Image src={star} alt="Image" />
                  <InputText
                    name="initial"
                    className={style.inputText}
                    value={configValue.initial}
                    placeholder="Enter your Initial"
                    disabled={true}
                  />
                </div>
              </div>
              <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                <div className={style.tabHead}>Preview</div>
                <div className={style.change} onClick={handleStyleChangeClick}>
                  Change style
                </div>
                {isCardVisible && (
                  <div className={style.card}>
                    <div className={style.dropList}>
                      {fontOptions.map((option) => (
                        <label key={option.value} className={style.list}>
                          <input
                            type="radio"
                            name="styleOption"
                            value={option.value}
                            onChange={() => handleFontFamilyChange(option.value)}
                            defaultChecked={selectedFont === option.value}
                          />
                          <div className={style.fontlist} style={{ fontFamily: option.value }}>
                            {configValue.legalName}
                          </div>
                          <div className={style.fontlist} style={{ fontFamily: option.value }}>
                            {configValue.initial}
                          </div>
                        </label>
                      ))}
                    </div>
                  </div>
                )}
              </div>
              <div className={style.headDraw}>
                <div
                  id="SignatureType"
                  className={style.uploadType}
                  style={{
                    width: '60%',
                    fontFamily: selectedFont
                  }}
                >
                  <span className={style.typedName}>{configValue.legalName}</span>
                </div>
                <div id="InitialType" className={style.uploadType} style={{ width: '40%', fontFamily: selectedFont }}>
                  <span className={style.typedName}>{configValue.initial}</span>
                </div>
              </div>
            </TabPanel>
            <TabPanel headerTemplate={<></>}>
              <div className={style.headerTemplate}>
                <div style={{ width: '60%' }}>
                  <label className={style.headerText}>Full Name</label>
                  <Image src={star} alt="Image"></Image>
                  <InputText
                    name="legalName"
                    value={configValue.legalName}
                    disabled={true}
                    onChange={changeHander}
                    className={style.inputText}
                  />
                </div>

                <div style={{ width: '40%' }}>
                  <label className={style.headerText}>Initials</label>
                  <Image src={star} alt="Image"></Image>
                  <InputText
                    name="initial"
                    value={configValue.initial}
                    disabled={true}
                    onChange={changeHander}
                    className={style.inputText}
                  />
                </div>
              </div>
              <div style={{ display: 'flex' }}>
                <div className={style.tabHead}>Preview</div>
                <div
                  className={style.signUpload}
                  onClick={(e) => {
                    fileUploaderSignature?.current?.click()
                  }}
                >
                  Upload Signature
                </div>
                <div
                  className={style.InitUpload}
                  onClick={(e) => {
                    fileUploaderInitial?.current?.click()
                  }}
                >
                  Upload Initials
                </div>
              </div>
              <div className={style.headDraw}>
                <div
                  className={clsx('flex align-items-center flex-column', style.uploadDraw)}
                  style={{ width: '60%' }}
                  onDragOver={handleDragOver}
                  onDrop={handleDropSignature}
                >
                  {signedUploadedFile !== undefined && signedUploadedFile !== null ? (
                    <>
                      <Image src={signedUploadedFile} height={500} width={200}></Image>
                      <span
                        className={style.clearUpload}
                        onClick={() => {
                          setSignedUploadedFile(null)
                        }}
                      >
                        {'Clear'}
                      </span>
                    </>
                  ) : (
                    <>
                      {' '}
                      <Image src={pen} alt="pen"></Image>
                      <div className={style.webText}>Drag & Drop your signature here or upload</div>
                      <input
                        ref={fileUploaderSignature}
                        type="file"
                        id="fileInput"
                        name="file"
                        accept=".gif,.jpg,.jpeg,.png,.bmp"
                        style={{ display: 'none' }}
                        onChange={handleFileUploadSignature}
                      />
                    </>
                  )}
                </div>

                <div
                  className={clsx('flex align-items-center flex-column', style.uploadDraw)}
                  style={{ width: '40%' }}
                  onDragOver={handleDragOver}
                  onDrop={handleDropInitial}
                >
                  {initialUploadedFile !== undefined && initialUploadedFile !== null ? (
                    <>
                      <Image src={initialUploadedFile} height={500} width={200}></Image>
                      <span
                        className={style.clearUploadInit}
                        onClick={() => {
                          setInitialUploadedFile(null)
                        }}
                      >
                        {'Clear'}
                      </span>
                    </>
                  ) : (
                    <>
                      {' '}
                      <Image src={pen} alt="pen"></Image>
                      <div className={style.webText}>Drag & Drop your Initials here or upload</div>
                      <input
                        ref={fileUploaderInitial}
                        type="file"
                        id="fileInput"
                        name="file"
                        accept=".gif,.jpg,.jpeg,.png,.bmp"
                        style={{ display: 'none' }}
                        onChange={handleFileUploadInitial}
                      />
                    </>
                  )}
                </div>
              </div>
              <div className={style.web_Text} style={{ marginTop: '10px' }}>
                Accepted file formats : JPG & PNG. Max file size: 5 MB
              </div>
            </TabPanel>
          </TabView>
          <div className={style.web_Text} style={{ margin: '1rem' }}>
            By clicking Create, I agree that the Signature and Initials will be electronic representation of my signatures and initials for
            all purpose when I use them, including legally binding contracts
          </div>
          <div className={style.buttonHeader}>
            <Button
              icon="pi pi-ban"
              outlined
              className={style.cancelButton}
              onClick={() => {
                setShow(false)
              }}
              style={{ gap: '10px' }}
            >
              {'Cancel'}
            </Button>
            <Button
              onClick={() => {
                if (activeIndex === 1) {
                  handlePredefinedImage()
                }
                if (activeIndex === 0) {
                  const stringSign = getSignatureImage(sigCanvasRef)
                  const stringInit = getSignatureImage(initialCanvasRef)
                  const cleanedStringSign = stringSign.replace('data:image/png;base64,', '')
                  const clearedStringInit = stringInit.replace('data:image/png;base64,', '')

                  const formData = new FormData()
                  formData.append('UserId', userProfile.userID)
                  formData.append('Type', ESignPreferenceType.Draw)
                  if (!sigCanvasRef.current.isEmpty()) {
                    formData.append('DrawSign', base64ToFile(cleanedStringSign, 'image/png', 'DrawSign.png'))
                  }
                  if (!initialCanvasRef.current.isEmpty()) {
                    formData.append('DrawIntial', base64ToFile(clearedStringInit, 'image/png', 'DrawInitial.png'))
                  }

                  if (!userProfile.selectedSign) {
                    postESignPrefMutate({ body: formData })
                  }
                } else if (activeIndex === 2) {
                  const formData = new FormData()
                  formData.append('UserId', userProfile.userID)
                  formData.append('Type', ESignPreferenceType.Upload)

                  if (signedUploadedFile !== null && signedUploadedFile !== undefined) {
                    const cleanedStringSign = signedUploadedFile.replace('data:image/png;base64,', '')

                    formData.append('UploadSign', base64ToFile(cleanedStringSign, 'image/png', 'UploadSign.png'))
                  }

                  if (initialUploadedFile !== null && initialUploadedFile !== undefined) {
                    const clearedStringInit = initialUploadedFile.replace('data:image/png;base64,', '')
                    formData.append('UploadIntial', base64ToFile(clearedStringInit, 'image/png', 'UploadInitial.png'))
                  }

                  if (!userProfile.selectedSign) postESignPrefMutate({ body: formData })
                }
              }}
              className={style.addButton}
              disabled={postESignPrefIsLoading}
            >
              {'+ Create'}
            </Button>
          </div>
        </div>
      </Modal>
    </>
  )
}

export function ViewUserSuggestion({ metadata, value, assignValuesNested, errors, onChange, componentDisabled }) {
  const [filteredUsers, setFilteredUsers] = useState([])
  const [selectedUser, setSelectedUser] = useState(value)

  const debounceTimeout = useRef(null) // Ref to store the timeout ID

  const searchUsers = (event) => {
    const query = event.query

    // Clear any existing timeout
    if (debounceTimeout.current) {
      clearTimeout(debounceTimeout.current)
    }

    // Set a new timeout
    debounceTimeout.current = setTimeout(async () => {
      if (query.trim().length > 0) {
        try {
          const users = await axiosGet(`/TenantRole/Approvers?searchText=${event.query}`)
          setFilteredUsers(users.data)
        } catch (error) {
          console.error('Error fetching user suggestions:', error)
          setFilteredUsers([])
        }
      } else {
        setFilteredUsers([]) // Clear suggestions if input is empty
      }
    }, 300) // Adjust debounce delay (e.g., 300ms)
  }

  const handleSelectionChange = (e) => {
    setSelectedUser(e.value) // Update local state
    assignValuesNested(name, e.value) // Update parent state if needed
    if (onChange) onChange(e) // Trigger additional onChange logic
  }

  const { label, validations, subtitle, alignment, name, disabled } = metadata
  return (
    <>
      <ComponentContainer>
        <AlignmentContainer alignment={alignment}>
          <LabelContainer
            style={{
              textAlign: alignment === 'row-reverse' ? 'right' : '',
              marginBottom: alignment === 'column' ? '0.5rem' : '',
              marginLeft: alignment === 'column' || 'row' ? '0.5rem' : ''
            }}
            alignment={alignment}
          >
            <Label label={label} validations={validations} />
          </LabelContainer>
          <InputsContainer>
            <AutoComplete
              style={{ width: '100%' }}
              name={name}
              disabled={disabled || componentDisabled}
              value={selectedUser}
              suggestions={filteredUsers}
              completeMethod={searchUsers}
              field="values.displayName" // Adjust based on API response
              onChange={handleSelectionChange}
              // placeholder="Search and select a user"
            />
            <Subtitle subtitle={subtitle} />
            <Errors errors={errors} />
          </InputsContainer>
        </AlignmentContainer>
      </ComponentContainer>
    </>
  )
}

export function ViewMultiRadioButtons({ metadata, value, onChange, errors, componentDisabled }) {
  const { name, label, subtitle, options, disabled, otherOptionEnabled, validations, alignment, isMultiColumn, divClassName } = metadata

  return (
    <ComponentContainer>
      <AlignmentContainer alignment={alignment}>
        <LabelContainer
          style={{
            textAlign: alignment === 'row-reverse' ? 'right' : '',
            marginBottom: alignment === 'column' ? '0.5rem' : '',
            marginLeft: alignment === 'column' || 'row' ? '0.5rem' : ''
          }}
          alignment={alignment}
          isCheckbox={true}
        >
          <Label label={label} validations={validations} />
        </LabelContainer>
        <InputsContainer
          style={{
            width: alignment === 'row-reverse' && !isMultiColumn ? 'auto' : '',
            justifyContent: alignment === 'row-reverse' && isMultiColumn ? 'flex-end' : ''
          }}
          className={clsx(
            isMultiColumn ? viewStyles.multiColumns : null,
            isMultiColumn && divClassName === 'col-11 mlr-05' ? viewStyles.radioButtonsFullSizeWidth : viewStyles.radioButtonsFullSizeWidth,
            isMultiColumn && divClassName !== 'col-11 mlr-05' ? viewStyles.radioButtonsHalfSizeWidth : viewStyles.radioButtonsHalfSizeWidth
          )}
        >
          <RadioButtons
            name={name}
            disabled={disabled || componentDisabled}
            options={options}
            otherEnabled={otherOptionEnabled}
            onChange={onChange}
            value={value}
          />
          <Subtitle subtitle={subtitle} />
          <Errors errors={errors} />
        </InputsContainer>
      </AlignmentContainer>
    </ComponentContainer>
  )
}

export function ViewCheckbox({ metadata, value, onChange, errors, componentDisabled }) {
  const { name, label, subtitle, disabled, options, alignment, validations, isMultiColumn, divClassName, otherOptionEnabled } = metadata

  return (
    <ComponentContainer>
      <AlignmentContainer alignment={alignment}>
        {label && (
          <LabelContainer
            alignment={alignment}
            style={{
              textAlign: alignment === 'row-reverse' ? 'right' : '',
              marginBottom: alignment === 'column' ? '0.5rem' : '',
              marginLeft: alignment === 'column' || 'row' ? '0.5rem' : ''
            }}
            isCheckbox={true}
          >
            <Label className="ml-4" label={label} validations={validations} />
          </LabelContainer>
        )}
        <InputsContainer
          className={clsx(
            !label && viewStyles.maxWidth,
            viewStyles.checkboxInputContainer,
            isMultiColumn && viewStyles.multiColumns,
            isMultiColumn && divClassName === 'col-11 mlr-05 ' && viewStyles.radioButtonsFullSizeWidth,
            isMultiColumn && divClassName !== 'col-11 mlr-05' && viewStyles.radioButtonsHalfSizeWidth
          )}
          // style={{ width: alignment === "row-reverse" && "auto"}}
          style={{
            width: alignment === 'row-reverse' && !isMultiColumn && 'auto',
            justifyContent: alignment === 'row-reverse' && isMultiColumn && 'flex-end'
          }}
        >
          {metadata.options.length > 0 && (
            <Checkboxes
              className={viewStyles.checkboxContainer}
              name={name}
              disabled={disabled || componentDisabled}
              options={options}
              onChange={onChange}
              value={value}
              otherEnabled={otherOptionEnabled}
            />
          )}
          <Subtitle subtitle={subtitle} />
          <Errors errors={errors} />
        </InputsContainer>
      </AlignmentContainer>
    </ComponentContainer>
  )
}

export function ViewAddress({ metadata, value, onChange, errors, componentDisabled, isForPayment = false, isForFormBuilder = false }) {
  const { name, label, subtitle, validations, disabled, permission, isFullSizeField } = metadata
  const [filteredSuggestions, setFilteredSuggestions] = useState([])
  const [addressValue, setAddressValue] = useState(value?.addressObj?.street_line ?? '')

  const addressStylesObj = {
    addressMargin: isForPayment ? viewStyles.paymentAddressMargin : viewStyles.addressMargin,
    headerComponent: isForPayment ? viewStyles.paymentHeaderComponent : viewStyles.headerComponent,
    viewAddressContainer: isForPayment ? viewStyles.viewPaymentContainer : viewStyles.viewAddressContainer,
    componentContainer: isForPayment ? null : viewStyles.addressComponentContainer,
    labelContainer: isForPayment ? viewStyles.paymentLabelContainer : viewStyles.addressLabelContainer,
    addressInputsLeft: isForPayment
      ? isForFormBuilder
        ? viewStyles.formbuilderPaymentAddressInputsLeft
        : viewStyles.paymentAddressInputsLeft
      : viewStyles.addressInputsLeft,
    addressInputsRight: isForPayment
      ? isForFormBuilder
        ? viewStyles.formbuilderPaymentAddressInputsRight
        : viewStyles.paymentAddressInputsRight
      : viewStyles.addressInputsRight
  }

  function handleAddressInputChange(e) {
    setAddressValue(e.target.value)

    const updatedValue = {
      ...value,
      addressObj: {
        street_line: e.target.value,
        city: '',
        state: '',
        zipcode: ''
      }
    }

    onChange({ target: { name: name, value: updatedValue } })
  }

  function handleCityChange(e) {
    setAddressValue('') // Clear local state for street_line

    const updatedValue = {
      ...value,
      addressObj: {
        street_line: '', // Clear street_line in value prop
        city: e.target.value,
        state: '',
        zipcode: ''
      }
    }

    onChange({ target: { name: name, value: updatedValue } })
  }

  function handleStateChange(e) {
    setAddressValue('') // Clear local state for street_line

    const updatedValue = {
      ...value,
      addressObj: {
        street_line: '', // Clear street_line in value prop
        city: '',
        state: e.target.value,
        zipcode: ''
      }
    }

    onChange({ target: { name: name, value: updatedValue } })
  }

  function handleZipChange(e) {
    setAddressValue('') // Clear local state for street_line

    const updatedValue = {
      ...value,
      addressObj: {
        street_line: '', // Clear street_line in value prop
        city: '',
        state: '',
        zipcode: e.target.value
      }
    }

    onChange({ target: { name: name, value: updatedValue } })
  }

  function handleSelectSuggestion(e) {
    const { street_line, city, state, zipcode } = e.value.addressObj

    setAddressValue(isFullSizeField ? `${street_line} ${city}, ${state} ${zipcode}` : street_line)

    const updatedValue = {
      ...value,
      addressObj: {
        ...value.addressObj,
        street_line,
        city,
        state,
        zipcode
      }
    }
    onChange({ target: { name: name, value: updatedValue } })
  }

  async function handleAddressFilter(event) {
    if (!event.query) return

    try {
      const results = await fetch('/form-builder-studio/api/smarty', {
        method: 'POST',
        body: JSON.stringify({ address: event.query }),
        headers: {
          'Content-Type': 'application/json'
        }
      }).then((response) => response.json())

      const filteredAddresses = results.suggestions.map((suggestion) => {
        const { streetLine: street_line, city, state, zipcode } = suggestion
        return {
          fullAddress: `${street_line} ${city}, ${state} ${zipcode}`,
          addressObj: { street_line, ...suggestion }
        }
      })

      setFilteredSuggestions(filteredAddresses)
    } catch (error) {
      console.error(error)
      setFilteredSuggestions([])
    }
  }

  return (
    <div className={addressStylesObj.headerComponent}>
      <div className={addressStylesObj.viewAddressContainer} style={{ gap: '0px', columnGap: '8%' }}>
        <LabelContainer className={`${viewStyles.addressLabel} ${viewStyles.addressLabelContainer} ${viewStyles.topSpacingLarge}`}>
          <Label label={label} validations={validations} />
        </LabelContainer>
      </div>
      <div className={isFullSizeField ? '' : addressStylesObj.addressInputsLeft}>
        <ComponentContainer>
          {!isFullSizeField && (
            <LabelContainer>
              <Label label="Street" validations={validations} />
            </LabelContainer>
          )}
          <InputsContainer>
            {permission && permission === 'Display in Masked' ? (
              <MaskPermissionInput />
            ) : (
              <>
                <AutoComplete
                  className={clsx(viewStyles.autoCompleteContainer, errors?.length > 0 && 'p-invalid')}
                  inputClassName={viewStyles.autoComplete}
                  field="fullAddress"
                  value={addressValue}
                  size={35}
                  suggestions={filteredSuggestions}
                  completeMethod={handleAddressFilter}
                  onChange={(e) => handleAddressInputChange(e)}
                  onSelect={(e) => handleSelectSuggestion(e)}
                  disabled={disabled || componentDisabled}
                />
                <Errors errors={errors} />
              </>
            )}
          </InputsContainer>
        </ComponentContainer>
      </div>
      {false && (
        <>
          <div className={addressStylesObj.addressInputsRight}>
            <ComponentContainer>
              <LabelContainer>
                <Label label="City" validations={validations} />
              </LabelContainer>
              <InputsContainer>
                <InputText
                  className={clsx(textStyles.inputText, errors?.length > 0 && 'p-invalid')}
                  value={value?.addressObj?.city}
                  style={{ width: '100%' }}
                  onChange={(e) => handleCityChange(e)}
                  disabled={disabled || componentDisabled}
                />
                <Errors errors={errors} />
              </InputsContainer>
            </ComponentContainer>
          </div>
          <div className={addressStylesObj.addressInputsLeft}>
            <ComponentContainer>
              <LabelContainer>
                <Label label="State" validations={validations} />
              </LabelContainer>
              <InputsContainer>
                <InputText
                  className={clsx(textStyles.inputText, errors?.length > 0 && 'p-invalid', viewStyles.maxWidth)}
                  value={value?.addressObj?.state}
                  onChange={(e) => handleStateChange(e)}
                  disabled={disabled || componentDisabled}
                />
                <Errors errors={errors} />
              </InputsContainer>
            </ComponentContainer>
          </div>
          <div className={addressStylesObj.addressInputsRight}>
            <ComponentContainer>
              <LabelContainer>
                <Label label="Zip" validations={validations} />
              </LabelContainer>
              <InputsContainer>
                <InputText
                  className={clsx(textStyles.inputText, errors?.length > 0 && 'p-invalid', viewStyles.maxWidth)}
                  value={value?.addressObj?.zipcode}
                  onChange={(e) => handleZipChange(e)}
                  disabled={disabled || componentDisabled}
                />
                <Errors errors={errors} />
              </InputsContainer>
            </ComponentContainer>
          </div>
        </>
      )}
      <div className={viewStyles.addressSubtitleContainer}>
        <Subtitle subtitle={subtitle} />
      </div>
    </div>
  )
}

export function ViewVendorDetails({
  metadata,
  value,
  onChange,
  errors,
  componentDisabled,
  isForPayment = false,
  isForFormBuilder = false
}) {
  const { name, label, subtitle, defaultValue, validations, disabled, permission } = metadata
  const [filteredSuggestions, setFilteredSuggestions] = useState([])
  const [addressValue, setAddressValue] = useState(value?.addressObj?.street_line ?? '')

  const addressStylesObj = {
    addressMargin: isForPayment ? viewStyles.paymentAddressMargin : viewStyles.addressMargin,
    headerComponent: isForPayment ? viewStyles.paymentHeaderComponent : viewStyles.headerComponent,
    viewAddressContainer: isForPayment ? viewStyles.viewPaymentContainer : viewStyles.viewAddressContainer,
    componentContainer: isForPayment ? null : viewStyles.addressComponentContainer,
    labelContainer: isForPayment ? viewStyles.paymentLabelContainer : viewStyles.addressLabelContainer,
    addressInputsLeft: isForPayment
      ? isForFormBuilder
        ? viewStyles.formbuilderPaymentAddressInputsLeft
        : viewStyles.paymentAddressInputsLeft
      : viewStyles.addressInputsLeft,
    addressInputsRight: isForPayment
      ? isForFormBuilder
        ? viewStyles.formbuilderPaymentAddressInputsRight
        : viewStyles.paymentAddressInputsRight
      : viewStyles.addressInputsRight
  }

  function handleAddressInputChange(e) {
    setAddressValue(e.target.value)

    const updatedValue = {
      ...value,
      addressObj: {
        name: e.target.value,
        city: '',
        state: '',
        zipcode: ''
      }
    }

    onChange({ target: { name: name, value: updatedValue } })
  }

  function handleCityChange(e) {
    setAddressValue('') // Clear local state for street_line

    const updatedValue = {
      ...value,
      addressObj: {
        street_line: '', // Clear street_line in value prop
        city: e.target.value,
        state: '',
        zipcode: ''
      }
    }

    onChange({ target: { name: name, value: updatedValue } })
  }

  function handleStateChange(e) {
    setAddressValue('') // Clear local state for street_line

    const updatedValue = {
      ...value,
      addressObj: {
        street_line: '', // Clear street_line in value prop
        city: '',
        state: e.target.value,
        zipcode: ''
      }
    }

    onChange({ target: { name: name, value: updatedValue } })
  }

  function handleZipChange(e) {
    setAddressValue('') // Clear local state for street_line

    const updatedValue = {
      ...value,
      addressObj: {
        street_line: '', // Clear street_line in value prop
        city: '',
        state: '',
        zipcode: e.target.value
      }
    }

    onChange({ target: { name: name, value: updatedValue } })
  }

  function handleSelectSuggestion(e) {
    const { code, email, phoneNumber } = e.value.addressObj

    // setAddressValue(e.value.)

    const updatedValue = {
      ...value,
      addressObj: {
        ...value?.addressObj,
        name: e.value?.addressObj?.name,
        code,
        email,
        phoneNumber,
        ...e.value.addressObj.Address
      }
    }
    onChange({ target: { name: name, value: updatedValue } })
  }

  async function handleAddressFilter(event) {
    if (!event.query) return

    try {
      const results = await axiosGet('PurchaseOrderRequistion/Vendor').then((response) => response.data.data.result)

      setTimeout(() => {
        let _filteredCountries

        if (!event.query.trim().length) {
          _filteredCountries = [...results]
        } else {
          _filteredCountries = [...results].filter((country) => {
            return country.itemdata.name.toLowerCase().startsWith(event.query.toLowerCase())
          })
        }

        setFilteredSuggestions(
          _filteredCountries.map((suggestion) => {
            const { name } = suggestion.itemdata

            return {
              fullAddress: name,
              addressObj: suggestion.itemdata
            }
          })
        )
      }, 0)
    } catch (error) {
      console.error(error)
      setFilteredSuggestions([])
    }
  }

  return (
    <div className={addressStylesObj.headerComponent}>
      <div className={addressStylesObj.viewAddressContainer} style={{ gap: '0px', columnGap: '8%' }}>
        <LabelContainer className={`${viewStyles.addressLabel} ${viewStyles.addressLabelContainer} ${viewStyles.topSpacingLarge}`}>
          <Label label={'Supplier Details'} validations={validations} />
        </LabelContainer>
      </div>
      <div className={addressStylesObj.addressInputsLeft}>
        <ComponentContainer>
          <LabelContainer>
            <Label label="Supplier Name" validations={validations} />
          </LabelContainer>
          <InputsContainer>
            {permission && permission === 'Display in Masked' ? (
              <MaskPermissionInput />
            ) : (
              <>
                <AutoComplete
                  className={clsx(viewStyles.autoCompleteContainer, errors?.length > 0 && 'p-invalid')}
                  inputClassName={clsx(textStyles.inputText, viewStyles.autoComplete)}
                  field="fullAddress"
                  value={value?.addressObj?.name}
                  size={35}
                  suggestions={filteredSuggestions}
                  completeMethod={handleAddressFilter}
                  onChange={(e) => handleAddressInputChange(e)}
                  onSelect={(e) => handleSelectSuggestion(e)}
                  disabled={disabled || componentDisabled}
                />
                <Errors errors={errors} />
              </>
            )}
          </InputsContainer>
        </ComponentContainer>
      </div>
      <div className={addressStylesObj.addressInputsRight}>
        <ComponentContainer>
          <LabelContainer>
            <Label label="Supplier Code" validations={validations} />
          </LabelContainer>
          <InputsContainer>
            <InputText
              className={clsx(textStyles.inputText, errors?.length > 0 && 'p-invalid')}
              value={value?.addressObj?.name ? value?.addressObj?.code : ''}
              style={{ width: '100%' }}
              onChange={(e) => handleCityChange(e)}
              disabled={disabled || componentDisabled}
              readOnly
            />
            <Errors errors={errors} />
          </InputsContainer>
        </ComponentContainer>
      </div>
      <div className={addressStylesObj.addressInputsLeft}>
        <ComponentContainer>
          <LabelContainer>
            <Label label="Supplier Email" validations={validations} />
          </LabelContainer>
          <InputsContainer>
            {permission && permission === 'Display in Masked' ? (
              <MaskPermissionInput />
            ) : (
              <>
                <InputText
                  className={clsx(textStyles.inputText, errors?.length > 0 && 'p-invalid')}
                  value={value?.addressObj?.name ? value?.addressObj?.email : ''}
                  style={{ width: '100%' }}
                  onChange={(e) => handleCityChange(e)}
                  disabled={disabled || componentDisabled}
                  readOnly
                />
              </>
            )}
          </InputsContainer>
        </ComponentContainer>
      </div>
      <div className={addressStylesObj.addressInputsRight}>
        <ComponentContainer>
          <LabelContainer>
            <Label label="Contact No" validations={validations} />
          </LabelContainer>
          <InputsContainer>
            <InputText
              className={clsx(textStyles.inputText, errors?.length > 0 && 'p-invalid')}
              value={value?.addressObj?.name ? value?.addressObj?.phoneNumber : ''}
              style={{ width: '100%' }}
              onChange={(e) => handleCityChange(e)}
              disabled={disabled || componentDisabled}
              readOnly
            />
            <Errors errors={errors} />
          </InputsContainer>
        </ComponentContainer>
      </div>
      <div className={addressStylesObj.viewAddressContainer} style={{ gap: '0px', columnGap: '8%' }}>
        <LabelContainer className={`${viewStyles.addressLabel} ${viewStyles.addressLabelContainer} ${viewStyles.topSpacingLarge}`}>
          <Label label={'Supplier Address'} validations={validations} />
        </LabelContainer>
      </div>
      <div className={addressStylesObj.addressInputsLeft}>
        <ComponentContainer>
          <LabelContainer>
            <Label label="Street" validations={validations} />
          </LabelContainer>
          <InputsContainer>
            <InputText
              className={clsx(textStyles.inputText, errors?.length > 0 && 'p-invalid', viewStyles.maxWidth)}
              value={value?.addressObj?.name ? value?.addressObj?.street : ''}
              onChange={(e) => handleStateChange(e)}
              disabled={disabled || componentDisabled}
              readOnly
            />
            <Errors errors={errors} />
          </InputsContainer>
        </ComponentContainer>
      </div>
      <div className={addressStylesObj.addressInputsRight}>
        <ComponentContainer>
          <LabelContainer>
            <Label label="State" validations={validations} />
          </LabelContainer>
          <InputsContainer>
            <InputText
              className={clsx(textStyles.inputText, errors?.length > 0 && 'p-invalid', viewStyles.maxWidth)}
              value={value?.addressObj?.name ? value?.addressObj?.state : ''}
              onChange={(e) => handleZipChange(e)}
              disabled={disabled || componentDisabled}
              readOnly
            />
            <Errors errors={errors} />
          </InputsContainer>
        </ComponentContainer>
      </div>
      <div className={addressStylesObj.addressInputsLeft}>
        <ComponentContainer>
          <LabelContainer>
            <Label label="City" validations={validations} />
          </LabelContainer>
          <InputsContainer>
            <InputText
              className={clsx(textStyles.inputText, errors?.length > 0 && 'p-invalid', viewStyles.maxWidth)}
              value={value?.addressObj?.name ? value?.addressObj?.city : ''}
              onChange={(e) => handleZipChange(e)}
              disabled={disabled || componentDisabled}
              readOnly
            />
            <Errors errors={errors} />
          </InputsContainer>
        </ComponentContainer>
      </div>
      <div className={addressStylesObj.addressInputsRight}>
        <ComponentContainer>
          <LabelContainer>
            <Label label="Zip" validations={validations} />
          </LabelContainer>
          <InputsContainer>
            <InputText
              className={clsx(textStyles.inputText, errors?.length > 0 && 'p-invalid', viewStyles.maxWidth)}
              value={value?.addressObj?.name ? value?.addressObj?.zip : ''}
              onChange={(e) => handleZipChange(e)}
              disabled={disabled || componentDisabled}
              readOnly
            />
            <Errors errors={errors} />
          </InputsContainer>
        </ComponentContainer>
      </div>
      <div className={viewStyles.addressSubtitleContainer}>
        <Subtitle subtitle={subtitle} />
      </div>
    </div>
  )
}

export function ViewRequestors({ metadata, value, onChange, errors, componentDisabled, isForPayment = false, isForFormBuilder = false }) {
  const { name, label, subtitle, defaultValue, validations, disabled, permission } = metadata
  const [filteredSuggestions, setFilteredSuggestions] = useState([])
  const [addressValue, setAddressValue] = useState(value?.addressObj?.street_line ?? '')
  const { accounts } = useMsal()
  const account = useAccount(accounts[0] ?? {})

  const addressStylesObj = {
    addressMargin: isForPayment ? viewStyles.paymentAddressMargin : viewStyles.addressMargin,
    headerComponent: isForPayment ? viewStyles.paymentHeaderComponent : viewStyles.headerComponent,
    viewAddressContainer: isForPayment ? viewStyles.viewPaymentContainer : viewStyles.viewAddressContainer,
    componentContainer: isForPayment ? null : viewStyles.addressComponentContainer,
    labelContainer: isForPayment ? viewStyles.paymentLabelContainer : viewStyles.addressLabelContainer,
    addressInputsLeft: isForPayment
      ? isForFormBuilder
        ? viewStyles.formbuilderPaymentAddressInputsLeft
        : viewStyles.paymentAddressInputsLeft
      : viewStyles.addressInputsLeft,
    addressInputsRight: isForPayment
      ? isForFormBuilder
        ? viewStyles.formbuilderPaymentAddressInputsRight
        : viewStyles.paymentAddressInputsRight
      : viewStyles.addressInputsRight
  }
  useEffect(() => {
    if (!value) {
      axiosGet(`UserProfile/${account.username}`).then((res) => {
        onChange({
          target: {
            name: name,
            value: {
              ...value,
              addressObj: {
                requestor: res?.data?.displayName,
                department: res?.data?.departmentName,
                supervisor: res?.data?.manager?.displayName,
                street: '',
                city: '',
                state: '',
                zipcode: ''
              }
            }
          }
        })
      })
    }
  }, [])

  function handleAddressInputChange(e) {
    setAddressValue(e.target.value)

    const updatedValue = {
      ...value,
      addressObj: {
        street_line: e.target.value,
        city: '',
        state: '',
        zipcode: ''
      }
    }

    onChange({ target: { name: name, value: updatedValue } })
  }

  function handleCityChange(e) {
    setAddressValue('') // Clear local state for street_line

    const updatedValue = {
      ...value,
      addressObj: {
        street_line: '', // Clear street_line in value prop
        city: e.target.value,
        state: '',
        zipcode: ''
      }
    }

    onChange({ target: { name: name, value: updatedValue } })
  }

  function handleStateChange(e) {
    setAddressValue('') // Clear local state for street_line

    const updatedValue = {
      ...value,
      addressObj: {
        street_line: '', // Clear street_line in value prop
        city: '',
        state: e.target.value,
        zipcode: ''
      }
    }

    onChange({ target: { name: name, value: updatedValue } })
  }

  function handleZipChange(e) {
    setAddressValue('') // Clear local state for street_line

    const updatedValue = {
      ...value,
      addressObj: {
        street_line: '', // Clear street_line in value prop
        city: '',
        state: '',
        zipcode: e.target.value
      }
    }

    onChange({ target: { name: name, value: updatedValue } })
  }

  function handleSelectSuggestion(e) {
    const { street_line, city, state, zipcode } = e.value.addressObj

    setAddressValue(street_line)

    const updatedValue = {
      ...value,
      addressObj: {
        ...value.addressObj,
        street_line,
        city,
        state,
        zipcode
      }
    }
    onChange({ target: { name: name, value: updatedValue } })
  }

  async function handleAddressFilter(event) {
    if (!event.query) return

    try {
      const results = await fetch('/form-builder-studio/api/smarty', {
        method: 'POST',
        body: JSON.stringify({ address: event.query }),
        headers: {
          'Content-Type': 'application/json'
        }
      }).then((response) => response.json())

      const filteredAddresses = results.result.map((suggestion) => {
        const { streetLine: street_line, city, state, zipcode } = suggestion
        return {
          fullAddress: `${street_line} ${city}, ${state} ${zipcode}`,
          addressObj: suggestion
        }
      })

      setFilteredSuggestions(filteredAddresses)
    } catch (error) {
      console.error(error)
      setFilteredSuggestions([])
    }
  }

  return (
    <div className={addressStylesObj.headerComponent}>
      <div className={addressStylesObj.viewAddressContainer} style={{ gap: '0px', columnGap: '8%' }}>
        <LabelContainer className={`${viewStyles.addressLabel} ${viewStyles.addressLabelContainer} ${viewStyles.topSpacingLarge}`}>
          <Label label={'Requestor Fields'} validations={validations} />
        </LabelContainer>
      </div>
      <div className={addressStylesObj.addressInputsLeft}>
        <ComponentContainer>
          <LabelContainer>
            <Label label="Requestor" validations={validations} />
          </LabelContainer>
          <InputsContainer>
            {permission && permission === 'Display in Masked' ? (
              <MaskPermissionInput />
            ) : (
              <>
                <InputText
                  className={clsx(textStyles.inputText, errors?.length > 0 && 'p-invalid')}
                  value={value?.addressObj?.requestor}
                  style={{ width: '100%' }}
                  onChange={(e) => handleCityChange(e)}
                  disabled={disabled || componentDisabled}
                  readOnly
                />
                {/* <Dropdown style={{ width: '100%' }} /> */}
                {/* <AutoComplete
                  className={clsx(
                    viewStyles.autoCompleteContainer,
                    errors?.length > 0 && "p-invalid"
                  )}
                  inputClassName={viewStyles.autoComplete}
                  field="fullAddress"
                  value={addressValue}
                  size={35}
                  suggestions={filteredSuggestions}
                  completeMethod={handleAddressFilter}
                  onChange={(e) => handleAddressInputChange(e)}
                  onSelect={(e) => handleSelectSuggestion(e)}
                  disabled={disabled || componentDisabled}
                /> */}
                <Errors errors={errors} />
              </>
            )}
          </InputsContainer>
        </ComponentContainer>
      </div>
      <div className={addressStylesObj.addressInputsRight}>
        <ComponentContainer>
          <LabelContainer>
            <Label label="Department" validations={validations} />
          </LabelContainer>
          <InputsContainer>
            {/* <Dropdown style={{ width: '100%' }} /> */}
            <InputText
              className={clsx(textStyles.inputText, errors?.length > 0 && 'p-invalid')}
              value={value?.addressObj?.department}
              style={{ width: '100%' }}
              onChange={(e) => handleCityChange(e)}
              disabled={disabled || componentDisabled}
              readOnly
            />
            <Errors errors={errors} />
          </InputsContainer>
        </ComponentContainer>
      </div>
      <div className={addressStylesObj.addressInputsLeft}>
        <ComponentContainer>
          <LabelContainer>
            <Label label="Supervisor" validations={validations} />
          </LabelContainer>
          <InputsContainer>
            {/* <Dropdown style={{ width: '100%' }} /> */}
            <InputText
              className={clsx(textStyles.inputText, errors?.length > 0 && 'p-invalid', viewStyles.maxWidth)}
              value={value?.addressObj?.supervisor}
              onChange={(e) => handleStateChange(e)}
              disabled={disabled || componentDisabled}
              readOnly
            />
            <Errors errors={errors} />
          </InputsContainer>
        </ComponentContainer>
      </div>
      <div className={viewStyles.addressSubtitleContainer}>
        <Subtitle subtitle={subtitle} />
      </div>
    </div>
  )
}

export function ViewPageBreak({ metadata, currentPage, setCurrentPage, objectKeysArray, isSubmissionPage, paginationErrors, checkErrors }) {
  const { backButtonText, nextButtonText, restrictNextPage, pageNumber } = metadata

  const buttonAlignment = currentPage === 0 ? 'flex-end' : currentPage == objectKeysArray.length - 1 ? 'flex-start' : 'space-between'

  const handleNextPageChange = () => {
    if (isSubmissionPage && restrictNextPage) {
      if (!checkErrors(paginationErrors, pageNumber)) {
        setCurrentPage((currentPageNum) => currentPageNum + 1)
      }
    } else {
      setCurrentPage((currentPageNum) => currentPageNum + 1)
    }
  }

  return (
    <ComponentContainer
      style={{
        justifyContent: 'flex-end',
        padding: '1rem 0',
        display: 'flex',
        flexDirection: 'row',
        columnGap: '20px'
      }}
    >
      {currentPage !== 0 && (
        <SecondaryButton
          icon="pi-chevron-left"
          iconSize="14px"
          iconPosition="left"
          text={backButtonText}
          onClick={() => setCurrentPage((currentPageNum) => currentPageNum - 1)}
        />
      )}
      {currentPage !== objectKeysArray.length - 1 && (
        <SecondaryButton
          icon="pi-angle-double-right"
          iconSize="18px"
          iconPosition="right"
          text={nextButtonText}
          onClick={handleNextPageChange}
        />
      )}
    </ComponentContainer>
  )
}

export const ViewTermsAndConditions = ({ metadata, wholeMetadata, onChange, errors, value, isSubmissionPage, setInputs }) => {
  const { name, subtitle, validations, disabled, guid, forceUserRead, labelText, permission } = metadata
  const [checked, setChecked] = useState(value ?? false)
  const errorContainerStyle = {
    display: 'flex',
    justifyContent: 'center',
    width: '34%'
  }
  const [termsAndConds, setTermsAndConds] = useState(false)

  useEffect(() => {
    setChecked(value)
  }, [value])

  const handleCheckboxChange = (event) => {
    const { name } = event.target
    const newChecked = !checked
    value = newChecked
    setChecked(newChecked)
    onChange({ target: { name: name, value: newChecked } })
  }

  function processString(inputString) {
    const regex = /{([^}]+)}/g

    const parts = inputString.split(regex).map((part, index) => {
      if (index % 2 === 1) {
        // if (!isSubmissionPage) {
        //   return part
        // }

        return (
          <span key={index} className={viewStyles.linkStyle} onClick={() => setTermsAndConds((prev) => !prev)}>
            {part}
          </span>
        )
      } else {
        return part
      }
    })

    return <label>{parts}</label>
  }

  return (
    <>
      <TermsAndConditionsModal
        isModalOpen={termsAndConds}
        onHide={() => setTermsAndConds((prev) => !prev)}
        value={metadata?.termsAndCondsText}
        guid={guid}
        isReadOnly={true}
        setInputs={setInputs}
        name={name}
        isButtonDisplayed={isSubmissionPage}
      />
      <div className={viewStyles.termsAndConditionsFlex}>
        <LabelContainer className={viewStyles.termsAndConditionsLabelContainer}>
          <span className={viewStyles.termsAndConditionsRequiredColor}>{validations?.required?.isRequired ? `* ` : null}</span>
          <CheckBox name={name} onChange={(event) => handleCheckboxChange(event)} checked={checked} disabled={disabled || forceUserRead} />
        </LabelContainer>
        <InputsContainer className={viewStyles.termsAndConditionsInputContainer}>
          <div>
            {permission === 'Display in Masked'
              ? '●●●●●●●●'
              : labelText
              ? processString(labelText)
              : processString('Click {here} to read our Terms and Conditions')}
          </div>
        </InputsContainer>
      </div>
      <Errors errors={errors} style={errorContainerStyle} />
    </>
  )
}

export const ViewCalculatedField = ({ metadata, inputs, wholeMetadata }) => {
  const { name, label, subtitle, calculation, alignment, permission } = metadata
  const { calculate } = useCalculatedFieldInputs()

  return (
    <ComponentContainer>
      <AlignmentContainer alignment={alignment}>
        <LabelContainer alignment={alignment}>
          <Label label={label} />
        </LabelContainer>
        <InputsContainer>
          {permission && permission === 'Display in Masked' ? (
            <MaskPermissionInput />
          ) : (
            <InputNumber
              name={name}
              value={calculate(calculation, inputs)}
              className={clsx(viewStyles.maxWidth, numberStyles.number)}
              useGrouping={false}
              disabled
            />
          )}
          <Subtitle subtitle={subtitle} />
        </InputsContainer>
      </AlignmentContainer>
    </ComponentContainer>
  )
}

export const ViewAdvancedFileUpload = ({
  metadata,
  files,
  value,
  onChange,
  errors,
  isMobile = false,
  componentDisabled,
  isSubmissionPage
}) => {
  const { name, label, subtitle, disabled, validations, fileTypes, alignment, permission } = metadata

  return (
    <ComponentContainer>
      <AlignmentContainer alignment={alignment}>
        <LabelContainer
          alignment={alignment}
          className={clsx(
            alignment === 'row' && `${textareaStyles.textareaLabel} ${textareaStyles.labelContainer}`,
            alignment === 'row-reverse' && labelStyles.marginLeft,
            viewStyles.topSpacingLarge
          )}
        >
          <Label label={label} validations={validations} />
        </LabelContainer>
        <InputsContainer className={textareaStyles.inputsContainer} style={{ width: '100%' }}>
          <AdvancedFileUpload
            name={name}
            fileTypes={fileTypes}
            value={value}
            disabled={disabled || componentDisabled}
            files={files?.[name]}
            onChange={onChange}
            isMobile={isMobile}
          />
          <Subtitle subtitle={subtitle} />
          <Errors errors={errors} />
        </InputsContainer>
      </AlignmentContainer>
    </ComponentContainer>
  )
}

export const ViewVersionedFileUpload = ({
  metadata,
  files,
  value,
  onChange,
  errors,
  isMobile = false,
  componentDisabled,
  isSubmissionPage
}) => {
  const { name, label, subtitle, disabled, validations, fileTypes, alignment } = metadata

  return (
    <ComponentContainer>
      <AlignmentContainer alignment={alignment}>
        <LabelContainer
          alignment={alignment}
          className={clsx(
            alignment === 'row' && `${textareaStyles.textareaLabel} ${textareaStyles.labelContainer}`,
            alignment === 'row-reverse' && labelStyles.marginLeft,
            viewStyles.topSpacingLarge
          )}
        >
          <Label label={label} validations={validations} />
        </LabelContainer>
        <InputsContainer className={textareaStyles.inputsContainer} style={{ width: '100%' }}>
          <FileInput
            name={name}
            fileTypes={fileTypes}
            value={value}
            disabled={disabled}
            files={files?.[name]}
            onChange={onChange}
            isMobile={isMobile}
            isSubmissionPage={isSubmissionPage}
          />
          <Subtitle subtitle={subtitle} />
          <Errors errors={errors} />
        </InputsContainer>
      </AlignmentContainer>
    </ComponentContainer>
  )
}

export const ViewTableComponent = ({ guid, metadata, headers, sourceTable, openDialog, value, onChange, assignValuesNested }) => {
  const { name, label, width, height, aspectRatio, file, alignment } = metadata
  const disabled = metadata?.disable ?? false
  let isViewMode = true

  const onRowUpdate = (name, value, resultObj) => {
    let finalObj = [{ resultObj: resultObj }, { cellData: value }]
    assignValuesNested(name, finalObj)
  }

  return (
    <ComponentContainer style={{ overflowX: 'auto' }}>
      <div>
        <Label label={label} />
        <TableComponent
          name={name}
          disabled={disabled}
          assignValuesNested={assignValuesNested}
          onRowUpdate={onRowUpdate}
          metadata={metadata}
          value={value}
          isViewMode={isViewMode}
        />
      </div>
    </ComponentContainer>
  )
}
export const ViewJsTableComponent = ({ guid, metadata, inputs, onChange, assignValuesNested, setMetadata, isApprovalPage }) => {
  const { name, label, width, height, aspectRatio, file, alignment, permission } = metadata
  // let isViewMode = true;
  // const onRowUpdate = (name, value, resultObj) => {
  //   let finalObj = [{ "resultObj": resultObj }, { "cellData": value }]
  //   assignValuesNested(name, finalObj)
  // }

  return (
    <ComponentContainer style={{ overflowX: 'auto' }}>
      <div>
        <Label label={label} />
        <JsTable
          name={name}
          guid={guid}
          metadata={metadata}
          wholeMetadata={inputs}
          setMetadata={setMetadata}
          handleInputChange={onChange}
          formSubmission={true}
          isApprovalPage={isApprovalPage}
        />
        {/* <TableComponent name={name} assignValuesNested={assignValuesNested} onRowUpdate={onRowUpdate} metadata={metadata} value={value} isViewMode={isViewMode} /> */}
      </div>
    </ComponentContainer>
  )
}

// export const ViewAutoComplete = ({ metadata, value, onChange, errors, componentDisabled }) => {
//   const { name, label, subtitle, disabled, options, validations, alignment, displayFields, selectedDataverseTable, displayFieldLabels, searchableField } = metadata
//   const [autocompleteValue, setAutocompleteValue] = useState('')
//   const [suggestions, setSuggestions] = useState([])
//   const { acquireToken } = useMsalAuthentication(InteractionType.Silent, dataverseApiRequest)

//   const handleSearch = async (event) => {
//     const { accessToken } = await acquireToken()
//     const selectQuery = displayFields?.map((field) => field).join(',') + `,${searchableField}`
//     const filterQuery = `$filter=contains(${searchableField}, '${event.query}')`
//     const response = await fetch(`${dataverseApi}${selectedDataverseTable}?$select=${selectQuery}&${filterQuery}&$top=5`, {
//       headers: {
//         'Authorization': `Bearer ${accessToken}`,
//         'OData-MaxVersion': '4.0',
//         'OData-Version': '4.0',
//         'Accept': 'application/json',
//         'Content-Type': 'application/json; charset=utf-8',
//       },
//       data: {
//         "name": "Ahmet",
//         "creditonhold": false,
//         "address1_latitude": 47.639583,
//         "description": "This is the description of the sample account",
//         "revenue": 5000000,
//         "accountcategorycode": 1
//       }
//     })

//     if (!response.ok) {
//       console.error('Error while getting the data')
//       setSuggestions(['google', 'microsoft', 'meta'])
//       return
//     }

//     const { value } = await response.json()
//     return setSuggestions(value)
//   }

//   const itemTemplate = (item) => {
//     return (
//       <div>
//         <div>{item?.[searchableField]}</div>
//       </div>
//     )
//   }

//   const handleValueChange = (e) => {
//     console.log('form comp', metadata)
//     if (typeof e.value === 'string') {
//       setAutocompleteValue(e.value)
//     } else if (typeof e.value === 'object' && e.value !== null) {
//       onChange(e)
//       setAutocompleteValue(e.value?.[searchableField])
//     }
//   }

//   return (
//     <ComponentContainer className={viewStyles.headerComponent}>
//       <div className={viewStyles.autoCompleteContainer} style={{ gap: '0px', columnGap: '8%' }}>
//         <div style={{ display: 'flex', width: '46%', marginTop: '0.5rem' }}>
//           <AlignmentContainer alignment={alignment}>
//             <LabelContainer>
//               <Label label={label} validations={validations} />
//             </LabelContainer>
//             <InputsContainer>
//               <AutoComplete
//                 name={name}
//                 style={{ width: '100%', height: '42px' }}
//                 inputStyle={{ width: '97%' }}
//                 value={autocompleteValue}
//                 itemTemplate={itemTemplate}
//                 onChange={handleValueChange}
//                 suggestions={suggestions}
//                 completeMethod={handleSearch}
//                 field='label'
//                 disabled={disabled || componentDisabled}
//               />
//               <Subtitle subtitle={subtitle} />
//               <Errors errors={errors} />
//             </InputsContainer>
//           </AlignmentContainer>
//         </div>
//         {displayFields?.map((field, index) => {
//           return (
//             <div style={{ display: 'flex', width: '46%', marginTop: '0.5rem' }} key={index}>
//               <AlignmentContainer alignment={alignment}>
//                 <LabelContainer>
//                   <Label label={displayFieldLabels?.[field]}></Label>
//                 </LabelContainer>
//                 <InputsContainer>
//                   <InputText
//                     className={clsx(viewStyles.autoCompleteInputText, textStyles.inputText)}
//                     style={{ backgroundColor: "#E5E8EA" }}
//                     name={`${field}_${index}`}
//                     disabled={true}
//                     options={options}
//                     onChange={onChange}
//                     value={value?.[field] ?? 'N/A'}
//                   />
//                 </InputsContainer>
//               </AlignmentContainer>
//             </div>
//           )
//         })}
//       </div>

//     </ComponentContainer>
//   )
// }
export const ViewAutoComplete = ({ metadata, value, onChange, errors, componentDisabled, setInputs }) => {
  const {
    name,
    label,
    subtitle,
    disabled,
    options,
    validations,
    alignment,
    displayFields,
    selectedDataverseTable,
    displayFieldLabels,
    searchableField
  } = metadata
  const [autocompleteValue, setAutocompleteValue] = useState(value?.[searchableField] ?? '')
  const [suggestions, setSuggestions] = useState([])
  const { acquireToken } = useMsalAuthentication(InteractionType.Silent, dataverseApiRequest)

  const handleSearch = async (event) => {
    const { accessToken } = await acquireToken()
    const selectQuery = displayFields?.map((field) => field).join(',') + `,${searchableField}`
    const filterQuery = `$filter=contains(${searchableField}, '${event.query}')`
    const response = await fetch(`${dataverseApi}${selectedDataverseTable}?$select=${selectQuery}&${filterQuery}&$top=5`, {
      headers: {
        Authorization: `Bearer ${accessToken}`,
        'OData-MaxVersion': '4.0',
        'OData-Version': '4.0',
        Accept: 'application/json',
        'Content-Type': 'application/json; charset=utf-8'
      },
      data: {
        name: 'Ahmet',
        creditonhold: false,
        address1_latitude: 47.639583,
        description: 'This is the description of the sample account',
        revenue: 5000000,
        accountcategorycode: 1
      }
    })

    if (!response.ok) {
      console.error('Error while getting the data')
      setSuggestions(['google', 'microsoft', 'meta'])
      return
    }

    const { value } = await response.json()
    return setSuggestions(value)
  }

  const itemTemplate = (item) => {
    return (
      <div>
        <div>{item?.[searchableField]}</div>
      </div>
    )
  }

  const handleValueChange = (e) => {
    if (typeof e.value === 'string' && e.value.trim() === '') {
      setInputs((prev) => {
        const clearedFields = { ...prev }
        Object.keys(clearedFields).forEach((key) => {
          clearedFields[key] = ''
        })
        return clearedFields
      })
      setAutocompleteValue('')
    } else if (typeof e.value === 'object' && e.value !== null) {
      onChange(e)
      setAutocompleteValue(e.value?.[searchableField])

      setInputs((prev) => {
        const updatedFields = { ...prev }
        updatedFields['text_4f8fa7b9-de9f-4865-8b0e-7bef851c175e'] = e.value?.[searchableField] || ''
        return updatedFields
      })
    } else {
      setAutocompleteValue(typeof e.value === 'string' ? e.value : '')
    }
  }

  return (
    <ComponentContainer>
      <div className={viewStyles.autoCompleteContainer}>
        <div className={viewStyles.autoCompleteFieldsContainer}>
          <AlignmentContainer alignment={alignment}>
            <LabelContainer alignment={alignment}>
              <Label label={label} validations={validations} />
            </LabelContainer>
            <InputsContainer>
              <AutoComplete
                name={name}
                style={{ width: '100%' }}
                inputClassName={viewStyles.autoComplete}
                value={autocompleteValue}
                itemTemplate={itemTemplate}
                onChange={handleValueChange}
                suggestions={suggestions}
                completeMethod={handleSearch}
                field="label"
                disabled={disabled || componentDisabled}
              />
              <Subtitle subtitle={subtitle} />
              <Errors errors={errors} />
            </InputsContainer>
          </AlignmentContainer>
        </div>
        {displayFields?.map((field, index) => {
          return (
            <div className={viewStyles.autoCompleteFieldsContainer} key={index}>
              <AlignmentContainer alignment={alignment}>
                <LabelContainer alignment={alignment}>
                  <Label label={displayFieldLabels?.[field]}></Label>
                </LabelContainer>
                <InputsContainer>
                  <InputText
                    style={{
                      backgroundColor: '#E5E8EA',
                      height: '43px',
                      width: '100%'
                    }}
                    name={`${field}_${index}`}
                    disabled={true}
                    options={options}
                    onChange={onChange}
                    value={value?.[field] ?? 'N/A'}
                  />
                </InputsContainer>
              </AlignmentContainer>
            </div>
          )
        })}
      </div>
    </ComponentContainer>
  )
}

export function ViewScaleRating({ metadata, value, onChange, errors, componentDisabled, isSubmissionPage }) {
  const { name, label, subtitle, validations, columns, rows, alignment, disabled } = metadata

  const convertedValue = value?.$numberInt ? Number(value.$numberInt) : value
  const componentContainerStyle = { flexWrap: 'nowrap', gap: '5px' }
  const labelContainerStyle = { width: '12.9%' }
  const inputContainerStyle = { width: '84.5%' }

  return (
    <ComponentContainer style={componentContainerStyle}>
      <AlignmentContainer alignment={alignment}>
        <LabelContainer style={labelContainerStyle} alignment={alignment}>
          <Label label={label} validations={validations} />
        </LabelContainer>
        <InputsContainer style={inputContainerStyle}>
          <NumberScale
            columns={columns}
            rows={rows}
            name={name}
            value={isSubmissionPage ? value : convertedValue}
            onChange={onChange}
            disabled={disabled || componentDisabled}
            isView={true}
          />
          <Subtitle subtitle={subtitle} />
          <Errors errors={errors} />
        </InputsContainer>
      </AlignmentContainer>
    </ComponentContainer>
  )
}

export function ViewStarRating({ metadata, value, onChange, errors, componentDisabled, isSubmissionPage }) {
  const { name, label, subtitle, validations, alignment, disabled } = metadata
  const convertedValue = value?.$numberInt ? Number(value.$numberInt) : value

  return (
    <ComponentContainer>
      <AlignmentContainer alignment={alignment}>
        <LabelContainer alignment={alignment}>
          <Label label={label} validations={validations} />
        </LabelContainer>
        <InputsContainer
          style={{
            paddingLeft: alignment === 'column' ? 0 : '0.5rem',
            width: alignment === 'row-reverse' ? '31%' : '66.666%'
          }}
        >
          <Rating
            name={name}
            value={isSubmissionPage ? value : convertedValue}
            onChange={onChange}
            className={clsx(viewStyles.maxWidth, textStyles.inputText, errors?.length > 0 && 'p-invalid')}
            cancel={false}
            disabled={disabled || componentDisabled}
          />
          <Subtitle subtitle={subtitle} />
          <Errors errors={errors} />
        </InputsContainer>
      </AlignmentContainer>
    </ComponentContainer>
  )
}

export function CreateTableComponent({
  guid,
  metadata,
  headers,
  sourceTable,
  openDialog,
  value,
  onChange,
  assignValuesNested,
  nestedAssignMetadata,
  handleUpdateMetadata
}) {
  const { name, label, width, height, aspectRatio, file, alignment } = metadata

  const onRowUpdate = (resultObj, value) => {
    nestedAssignMetadata(`${guid}.tableComponent`, [{ resultObj: resultObj }, { cellData: value }])
    // assignValuesNested(`${guid}.tableComponent`, value)
  }

  const handleValueChange = (e) => {
    nestedAssignMetadata(`${guid}.tableComponent`, [{ resultObj: resultObj }, { cellData: value }])
    assignValuesNested(`${guid}.tableComponent`, value)
  }

  return (
    <ComponentContainer onClick={() => openDialog(metadata)}>
      <div>
        {/* <SettingsButton openDialog={openDialog} componentData={metadata} /> */}
        <TableComponent
          name={name}
          onRowUpdate={onRowUpdate}
          metadata={metadata}
          onChange={handleValueChange}
          value={value}
          assignValuesNested={assignValuesNested}
        />
      </div>
    </ComponentContainer>
  )
}

const PaymentTextFieldRow = ({ label, name, errors, value, onChange, validations }) => {
  return (
    <div className={paymentStyles.paymentFieldRowGaps}>
      <Label label={label} validations={validations} />
      <InputText className={clsx(maskStyles.mask, errors?.length > 0 && 'p-invalid')} name={name} value={value} onChange={onChange} />
      <Errors errors={errors} />
    </div>
  )
}

const PaymentMaskFieldRow = ({ label, name, mask, errors, value, onChange, validations, width = 'auto' }) => {
  return (
    <div className={paymentStyles.paymentFieldRowGaps} style={{ width: width }}>
      <Label label={label} validations={validations} />
      <InputMask
        className={clsx(maskStyles.mask, errors?.length > 0 && 'p-invalid')}
        mask={mask}
        name={name}
        value={value}
        onChange={onChange}
      />
      <Errors errors={errors} />
    </div>
  )
}

export function ViewPayments({ metadata, value, onChange, errors, isSubmissionPage }) {
  const { name, label, description, validations, address, price, disabled } = metadata
  const handlePaymentDetailsChange = (event) => {
    const { name: paymentName, value: paymentValue } = event.target

    onChange({
      target: {
        name: name,
        value: {
          paymentDetails: {
            ...value?.paymentDetails,
            [paymentName]: paymentValue
          },
          address: value?.address || ''
        }
      }
    })
  }

  const handlePaymentAddressChange = (event) => {
    const { value: paymentValue } = event.target

    onChange({
      target: {
        name: name,
        value: {
          paymentDetails: value?.paymentDetails || {},
          address: {
            ...paymentValue
          }
        }
      }
    })
  }

  const handleCurrencyProperties = (propertyType, currencyLabel) => {
    if (propertyType === 'inputId') {
      return `currency-${
        currencyLabel.includes('USD') ? 'us' : currencyLabel.includes('₹') ? 'india' : currencyLabel.includes('€') ? 'germany' : 'us'
      }`
    }

    if (propertyType === 'currency') {
      return `${currencyLabel.includes('USD') ? 'USD' : currencyLabel.includes('₹') ? 'INR' : currencyLabel.includes('€') ? 'EUR' : 'USD'}`
    }

    if (propertyType === 'locale') {
      return `${
        currencyLabel.includes('USD') ? 'en-US' : currencyLabel.includes('₹') ? 'en-IN' : currencyLabel.includes('€') ? 'de-DE' : 'en-US'
      }`
    }
  }

  const currencyProperties = {
    inputId: handleCurrencyProperties('inputId', metadata.currentCurrency),
    currency: handleCurrencyProperties('currency', metadata.currentCurrency),
    locale: handleCurrencyProperties('locale', metadata.currentCurrency)
  }

  return (
    <div>
      <div>
        <h3>Product</h3>
        <div className={paymentStyles.itemSectionContainer}>
          <div className={paymentStyles.nameDescription}>
            <Label label={label} />
            <Label label={description} />
          </div>
          <InputNumber
            style={{ width: '29.5%' }}
            className={clsx(numberStyles.number, errors?.length > 0 && 'p-invalid')}
            value={price}
            disabled={!validations.canUserChangePrice || disabled}
            mode="currency"
            inputId={`currency-${currencyProperties.inputId}`}
            currency={`${currencyProperties.currency}`}
            locale={`${currencyProperties.locale}`}
          />
        </div>
      </div>

      <div>
        <h3>Payment Details</h3>
        {isSubmissionPage && (
          <div className={paymentStyles.paymentDetailsSectionContainer}>
            <PaymentTextFieldRow
              label={'Name on Card'}
              name={'nameOnCard'}
              errors={errors}
              value={value?.paymentDetails?.nameOnCard ?? ''}
              onChange={handlePaymentDetailsChange}
              validations={validations}
            />
            <PaymentMaskFieldRow
              label={'Card Number'}
              name={'cardNumber'}
              errors={errors}
              mask={'9999-9999-9999-9999'}
              value={value?.paymentDetails?.cardNumber ?? ''}
              onChange={handlePaymentDetailsChange}
              validations={validations}
            />
            <div className={paymentStyles.cardExpirationAndSecurityCodeContainer}>
              <PaymentMaskFieldRow
                label={'Card Expiration'}
                name={'cardExpiration'}
                errors={errors}
                mask={'99/99'}
                value={value?.paymentDetails?.cardExpiration ?? ''}
                onChange={handlePaymentDetailsChange}
                validations={validations}
                width={'50%'}
              />
              <PaymentMaskFieldRow
                label={'Security Code'}
                name={'securityCode'}
                errors={errors}
                mask={'999'}
                value={value?.paymentDetails?.securityCode ?? ''}
                onChange={handlePaymentDetailsChange}
                validations={validations}
                width={'50%'}
              />
            </div>
            <PaymentTextFieldRow
              label={'Email Address'}
              name={'email'}
              errors={errors}
              value={value?.paymentDetails?.email ?? ''}
              onChange={handlePaymentDetailsChange}
              validations={validations}
            />
          </div>
        )}
      </div>

      <div>
        <h3>Billing Address</h3>
        {isSubmissionPage && (
          <ViewAddress
            metadata={address}
            errors={errors}
            value={value?.address || {}}
            onChange={handlePaymentAddressChange}
            isForPayment={true}
          />
        )}
      </div>
    </div>
  )
}

export function ViewHeading({ metadata }) {
  const { subtitle, alignment } = metadata

  return (
    <div className={viewStyles.subtitleMargin}>
      <AlignmentContainer alignment={alignment} isHeading={true}>
        <ReadonlyLexicalEditor value={subtitle} />
      </AlignmentContainer>
    </div>
  )
}

export function ViewEmployeeLookup({ metadata, value, setInputs, onChange, errors, isSubmissionPage }) {
  const { name, label, subtitle, validations, displayFields, displayFieldLabels, alignment } = metadata
  // const { formatDateToMMDDYYYY } = useUtilityFunctions()
  const router = useRouter()
  const [employees, setEmployees] = useState([])
  // TODO: Delete the three below variables after asset tracker demos.
  const hasRunOnce = useRef(false)
  const tempAssetLoanEmployeeOptions = [{ fullLegalName: '1112 - Tommy Evans' }]
  const tempAssetLoanAutoComplete = {
    'text_8026e9c9-2f9d-4a1d-a4d8-1257b19ca914': '1112',
    'text_a9ff338f-323a-44eb-97cd-4cf2e2dcb615': 'Tommy',
    'text_e19f2885-1383-4937-bd8f-a5b2d1514a23': 'Evans',
    'text_520bc5f4-1a0f-49fe-8d32-476101ac8fbb': 'IT',
    'mask_dc7c5573-8f28-450c-8ed9-08c03a5517f0': '(000) 000-0000',
    'text_64e7e13e-7d8a-4010-a3ff-5750c6ece693': 'Full Time'
  }

  const handleSearch = async (event) => {
    const query = event?.query.toLowerCase()

    if (router.asPath.includes('25d1b09f-296c-4486-b744-8573615b8458')) {
      setEmployees(tempAssetLoanEmployeeOptions)
    } else {
      const filteredEmployees = OnBoardingEmployeeOptions.filter(
        (employee) => employee.fullLegalName.toLowerCase().includes(query) || employee.employeeID.includes(query)
      )

      setEmployees(filteredEmployees)
    }
  }

  // TODO: Re-implement this once the values in the OnBoardingEmployeeOptions file are added to the table associated with the WorkdayEmployee/Filter API. - Alex
  // const handleSearch = async (event) => {
  //   try {
  //     const accessToken = await getAccessTokenForScopeSilent(formBuilderStudioApi)
  //     const workdayApiCall = await fetch(`${formBuilderStudioApi}WorkdayEmployee/Filter`, {
  //       method: "POST",
  //       body: JSON.stringify({
  //         page: 0,
  //         rows: 5,
  //         sortField: "",
  //         sortOrder: 1,
  //         global: event?.query
  //       }),
  //       headers: {
  //         'Authorization': `Bearer ${accessToken}`,
  //         "Content-Type": "application/json",
  //         'accept': '*/*',
  //       },
  //     })

  //     const results = await workdayApiCall.json()
  //     const updatedResults = results?.workdayEmployees.map(employee => {
  //       employee.effectiveTransferHireDate = formatDateToMMDDYYYY(employee.effectiveTransferHireDate)
  //       employee.department = defaultEmployeesObj[0].department
  //       employee.supervisor = defaultEmployeesObj[0].supervisor
  //       employee.accountStatus = defaultEmployeesObj[0].accountStatus
  //       employee.telecommute = defaultEmployeesObj[0].telecommute
  //       employee.hiringType = defaultEmployeesObj[0].hiringType
  //       employee.employeeType = defaultEmployeesObj[0].employeeType
  //       // ^These fields besides effectiveTransferHireDate are not inlcuded in the workday employee objects. Once they are updated to have these fields we can remove
  //       // these declarations. - Alex

  //       return {
  //         ...employee,
  //         fullLegalName: `${employee?.legalFirstName} ${employee?.legalLastName}`
  //       }
  //     })

  //     setEmployees(updatedResults)
  //   } catch (error) {
  //     console.error(error)
  //     setEmployees(defaultEmployeesObj)
  //   }
  // }

  useEffect(() => {
    if (
      !hasRunOnce.current &&
      router.asPath.includes('25d1b09f-296c-4486-b744-8573615b8458') &&
      value?.fullLegalName === '1112 - Tommy Evans'
    ) {
      hasRunOnce.current = true
      setInputs((prev) => {
        const temp = JSON.parse(JSON.stringify(prev))
        return {
          ...temp,
          ...tempAssetLoanAutoComplete
        }
      })
    }
  }, [value])

  return (
    <ComponentContainer>
      <div className={viewStyles.autoCompleteContainer}>
        {/* TODO: Remove the conditional logic used for the asset loan page when demos are done. */}
        <div
          className={
            router.asPath.includes('25d1b09f-296c-4486-b744-8573615b8458') ? viewStyles.maxWidth : viewStyles.autoCompleteFieldsContainer
          }
        >
          <AlignmentContainer alignment={alignment}>
            <LabelContainer alignment={alignment}>
              <Label label={label} validations={validations} />
            </LabelContainer>
            <InputsContainer>
              <AutoComplete
                name={name}
                style={{ width: '100%' }}
                inputClassName={viewStyles.autoComplete}
                value={value}
                onChange={onChange}
                suggestions={employees}
                completeMethod={handleSearch}
                field="fullLegalName"
                disabled={!isSubmissionPage}
              />
              <Subtitle subtitle={subtitle} />
              <Errors errors={errors} />
            </InputsContainer>
          </AlignmentContainer>
        </div>
        {!router.asPath.includes('25d1b09f-296c-4486-b744-8573615b8458') &&
          displayFields?.map((field, index) => {
            return (
              <div className={viewStyles.autoCompleteFieldsContainer} key={index}>
                <AlignmentContainer alignment={alignment}>
                  <LabelContainer alignment={alignment}>
                    <Label label={displayFieldLabels?.[field]}></Label>
                  </LabelContainer>
                  <InputsContainer>
                    <InputText
                      style={{
                        backgroundColor: '#E5E8EA',
                        height: '43px',
                        width: '100%'
                      }}
                      name={`${field}_${index}`}
                      disabled={true}
                      value={value?.[field] ?? ''}
                    />
                  </InputsContainer>
                </AlignmentContainer>
              </div>
            )
          })}
      </div>
    </ComponentContainer>
  )
}

export function ViewAccordion({
  metadata,
  wholeMetadata,
  inputs,
  onChange,
  assignValuesNested,
  errors,
  wholeErrors,
  checkErrors,
  formSubmission,
  formSubmissionData,
  // disabled,
  currentPage,
  setCurrentPage,
  isSubmissionPage = false,
  files,
  nestedAssignMetadata,
  setInputs,
  pageData,
  objectKeysArray,
  metadataWithPermissions,
  key
}) {
  const { label, accordionTabs, guid, disabled } = metadata

  return (
    <div className={viewStyles.subtitleMargin}>
      <Accordion
        sections={accordionTabs}
        guid={guid}
        isSubmissionPage={true}
        metadata={metadata}
        inputs={inputs}
        handleInputChange={onChange}
        assignValuesNested={assignValuesNested}
        errors={wholeErrors}
        checkErrors={checkErrors}
        formSubmission={formSubmission}
        formSubmissionData={formSubmissionData}
        disabled={disabled}
        currentPage={currentPage}
        setCurrentPage={setCurrentPage}
        files={files}
        nestedAssignMetadata={nestedAssignMetadata}
        setInputs={setInputs}
        pageData={pageData}
        objectKeysArray={objectKeysArray}
        metadataWithPermissions={metadataWithPermissions}
        key={key}
      />
    </div>
  )
}

export function ViewObjectLink({ value, onChange, metadata, errors }) {
  const {
    label,
    subtitle,
    alignment,
    divClassName,
    sourceFormData,
    primarySearchField,
    resultFields,
    validations,
    name,
    allowMultiSelectForResults
  } = metadata

  return (
    <ComponentContainer>
      <AlignmentContainer alignment={alignment}>
        <LabelContainer alignment={alignment}>
          <Label label={label} validations={validations} />
        </LabelContainer>
        <InputsContainer divClassName={divClassName}>
          <ObjectLinkDropdown
            allowMultiSelectForResults={allowMultiSelectForResults}
            primarySearchField={primarySearchField}
            resultFields={resultFields}
            sourceFormData={sourceFormData}
            componentName={name}
            componentValue={value || ''}
            onChange={onChange}
            className={clsx(viewStyles.maxWidth, dropdownStyles.dropdown, errors?.length > 0 && 'p-invalid')}
          />
          <Subtitle subtitle={subtitle} />
        </InputsContainer>
      </AlignmentContainer>
    </ComponentContainer>
  )
}

export function ViewObjectLinkGrid({ metadata, setInputs, value }) {
  // TODO: Consider splitting this large component into smaller, focused components:
  // 1. FooterCalculator - Handles all footer field calculations
  // 2. RowManager - Handles row operations (add, delete, batch)
  // 3. ColumnRenderer - Handles different column type templates
  // 4. DataTableWrapper - Handles DataTable configuration and rendering
  // 5. FormulaEvaluator - Handles formula calculations

  // TODO: Consider using React Context for shared state between components
  // This would reduce prop drilling and make state management clearer

  // TODO: Extract all template functions into a separate file
  // This would make the component more focused and easier to maintain

  // TODO: Consider using a reducer pattern for complex state management
  // This would make state updates more predictable and easier to debug

  // TODO: Add proper TypeScript interfaces for props and state
  // This would improve type safety and developer experience

  // TODO: Consider using custom hooks for:
  // - Form submission data fetching
  // - Dropdown options management
  // - Line items batch processing
  // - Footer calculations

  const { disabled } = metadata

  const extractPrimarySearchFields = (items) => {
    if (!items || items === '●●●●●●●●') return []

    return items.map((item) => item.primarySearchField)
  }

  const calculateFooterValues = () => {
    // If value is masked, don't return any footer values
    if (value === '●●●●●●●●') return []

    if (!metadata?.footerFields) return []

    // Get all unique column names that have footer fields
    const columnNames = Object.keys(metadata.footerFields)
    if (columnNames.length === 0) return []

    // Find the maximum number of footer rows needed
    const maxFooterRows = Math.max(...columnNames.map((col) => metadata.footerFields[col].length))

    // Create footer rows
    const footerRows = Array(maxFooterRows)
      .fill({})
      .map((_, rowIndex) => {
        const row = {}
        columnNames.forEach((col) => {
          const footerField = metadata.footerFields[col][rowIndex]
          if (footerField) {
            // Find the corresponding column in dataTableColumns
            const column = dataTableColumns.find((c) => c.header === col)
            if (!column) return

            if (footerField.type === 'calculation') {
              // For calculations, we need to evaluate the formula
              const formula = footerField.value

              const functionMatch = formula.match(/^([A-Za-z]+)\((.*)\)$/)
              if (functionMatch) {
                const [, functionName, args] = functionMatch

                // Handle range syntax [start:end]
                const rangeMatch = args.match(/\[(\d+|end):(\d+|end)\]$/)
                let columnName = args
                let range = null

                if (rangeMatch) {
                  columnName = args.replace(/\[.*\]/, '').trim()
                  const start = rangeMatch[1] === 'end' ? value?.length : parseInt(rangeMatch[1])
                  const end = rangeMatch[2] === 'end' ? value?.length : parseInt(rangeMatch[2])
                  range = {
                    start: start,
                    end: end
                  }
                }

                // Get all values from the rows
                const values = []

                if (value && value.length > 0) {
                  // For each row in the value array
                  value.forEach((rowData, rowIndex) => {
                    // Find all keys that match the column name pattern
                    const matchingColumn = dataTableColumns.find((c) => c.header === columnName)
                    if (!matchingColumn) {
                      return
                    }

                    const matchingKeys = Object.keys(rowData).filter((key) => {
                      // For additional columns, check if key starts with column name
                      if (matchingColumn.field.startsWith(`${columnName}_`)) {
                        return key.startsWith(columnName + '_')
                      }
                      // For result fields, check if key matches the field GUID
                      return key === matchingColumn.field
                    })

                    matchingKeys.forEach((key) => {
                      const val = rowData[key]
                      if (val !== undefined && val !== null) {
                        if (range) {
                          if (rowIndex >= range.start - 1 && rowIndex <= range.end - 1) {
                            const num = parseFloat(val)
                            if (!isNaN(num)) {
                              values.push(num)
                            }
                          }
                        } else {
                          const num = parseFloat(val)
                          if (!isNaN(num)) {
                            values.push(num)
                          }
                        }
                      }
                    })
                  })
                }

                let result = 0

                switch (functionName.toUpperCase()) {
                  case 'SUM':
                    result = values.reduce((a, b) => a + b, 0)
                    break
                  case 'AVG':
                    result = values.length > 0 ? values.reduce((a, b) => a + b, 0) / values.length : 0
                    break
                  case 'COUNT':
                    result = values.length
                    break
                  case 'PRODUCT':
                    result = values.length > 0 ? values.reduce((a, b) => a * b, 1) : 0
                    break
                }

                row[column.field] = result
              }
            } else {
              // For titles, just use the value directly
              row[column.field] = footerField.value
            }
          }
        })

        return row
      })

    return footerRows
  }

  const [formSubmissionData, setFormSubmissionData] = useState([])
  const [dropdownOptions, setDropdownOptions] = useState([])
  const [dropdownNames, setDropdownNames] = useState(extractPrimarySearchFields(value))
  const [isLineItemsModalVisible, setIsLineItemsModalVisible] = useState(false)
  const [lineItemsBatch, setLineItemsBatch] = useState([])
  const [selectedLineItems, setSelectedLineItems] = useState([])
  const {
    label,
    name,
    guid,
    subtitle,
    divClassName,
    alignment,
    validations,
    resultFields,
    primarySearchField,
    additionalColumns,
    dataTableColumns: dataTableColumnsFromMetadata,
    sourceFormData
  } = metadata
  const { createDashboardDate } = useUtilityFunctions()
  const dataTableColumns = mapFields([], primarySearchField, additionalColumns)

  const initialLazyParams = {
    first: 0,
    page: 0,
    rows: 50,
    sortField: 'lastUpdatedAtUtc',
    sortOrder: -1
  }
  const { lazyParamsToQueryString, lazyParams } = useDashboard(initialLazyParams)

  function mapFields(resultFields, primarySearchField, additionalColumns) {
    if (!resultFields || !primarySearchField) return []
    const mappedFields = []

    console.log('mapFields', metadata, inputs, resultFields, primarySearchField)
    // Add primary search field first
    if (primarySearchField?.name && primarySearchField?.label) {
      mappedFields.push({
        field: primarySearchField.name,
        header: primarySearchField.label
      })
    }

    // Map each result field
    resultFields.forEach((field) => {
      if (field?.id && field?.name) {
        mappedFields.push({
          field: field.id,
          header: field.name,
          dataType: field?.dataType,
          editable: field?.editable ?? false
        })
      }
    })

    // Map additional columns
    if (additionalColumns) {
      additionalColumns.forEach((column, index) => {
        if (column.title) {
          mappedFields.push({
            field: `${column.id ?? column?.title}`,
            header: column.title,
            dataType: column.dataType,
            dropdownOptions: column?.dropdownOptions ?? [],
            calculationFormula: column?.calculationFormula,
            hasCalculate: column?.hasCalculate,
            isRequired: column?.isRequired
          })
        }
      })
    }

    return mappedFields
  }

  const calculateValue = (formula, rowData) => {
    if (!formula || !rowData || Object.keys(rowData).length === 0) return null

    try {
      // Create a mapping of column titles to their field names
      const columnMap = {}
      dataTableColumns.forEach((column) => {
        if (column.field && column.header) {
          columnMap[column.header] = {
            field: column.field,
            dataType: column.dataType,
            // Check if the column name contains % or if it's a percentage type
            isPercentage: column.dataType === 'percentage' || column.header.includes('%')
          }
        }
      })

      // First, replace all column names with placeholders to protect them
      const columnPlaceholders = {}
      let placeholderCount = 0
      const formulaWithPlaceholders = Object.keys(columnMap).reduce((acc, columnName) => {
        const placeholder = `__COL${placeholderCount}__`
        columnPlaceholders[placeholder] = columnName
        placeholderCount++
        return acc.replace(new RegExp(columnName.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g'), placeholder)
      }, formula)

      // Replace column placeholders with their values
      const evaluatedFormula = formulaWithPlaceholders.replace(/__COL\d+__/g, (match) => {
        const columnName = columnPlaceholders[match]
        const columnInfo = columnMap[columnName]
        if (!columnInfo) return match

        // Get the value from rowData using the field name
        const value = rowData[columnInfo.field]
        if (value === undefined) return match

        // Handle different number formats
        let numericValue
        if (typeof value === 'object' && value?.$numberInt) {
          numericValue = parseInt(value.$numberInt)
        } else if (typeof value === 'object' && value?.$numberDecimal) {
          numericValue = parseFloat(value.$numberDecimal)
        } else if (typeof value === 'string') {
          numericValue = parseFloat(value)
        } else {
          numericValue = value
        }

        // Convert percentage to decimal if needed
        if (columnInfo.isPercentage) {
          // Remove any % sign and convert to decimal
          numericValue = parseFloat(numericValue.toString().replace('%', '')) / 100
        }

        return isNaN(numericValue) ? value : numericValue
      })

      // Evaluate the formula
      const result = new Function('return ' + evaluatedFormula)()

      // Ensure the result is a number
      const numericResult = parseFloat(result)
      return isNaN(numericResult) ? null : numericResult
    } catch (error) {
      console.error('Error calculating formula:', error)
      return null
    }
  }

  const calculatedTemplate = (column, rowInfo) => {
    const { rowIndex } = rowInfo
    const rowData = value?.[rowIndex] || {}

    if (!column.calculationFormula) return null

    // Check if rowData is empty
    if (Object.keys(rowData).length === 0) {
      return <span style={{ color: '#666' }}>No data available</span>
    }

    const calculatedValue = calculateValue(column.calculationFormula, rowData)

    // If we got null from calculateValue, it means some required values were missing
    if (calculatedValue === null) {
      // Create a mapping of column titles to their field names
      const columnMap = {}
      dataTableColumns.forEach((col) => {
        if (col.field && col.header) {
          columnMap[col.header] = col.field
        }
      })

      // First, replace all column names with placeholders to protect them
      const columnPlaceholders = {}
      let placeholderCount = 0
      const formulaWithPlaceholders = Object.keys(columnMap).reduce((acc, columnName) => {
        const placeholder = `__COL${placeholderCount}__`
        columnPlaceholders[placeholder] = columnName
        placeholderCount++
        return acc.replace(new RegExp(columnName.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g'), placeholder)
      }, column.calculationFormula)

      // Now split on operators and parentheses to get the parts
      const parts = formulaWithPlaceholders
        .split(/[\+\-\*\/\(\)]/)
        .map((part) => part.trim())
        .filter((part) => part && !/^\d+$/.test(part))

      // Convert placeholders back to column names
      const columnNames = parts.map((part) => {
        if (part in columnPlaceholders) {
          return columnPlaceholders[part]
        }
        return part
      })

      // Check which columns are missing values
      const missingColumns = columnNames.filter((name) => {
        const fieldName = columnMap[name]
        return !rowData[fieldName]
      })

      if (missingColumns.length > 0) {
        return <span style={{ color: '#666' }}>Missing values for: {missingColumns.join(', ')}</span>
      }

      return <span style={{ color: 'red' }}>Invalid calculation</span>
    }

    // Format the calculated value based on the column's dataType
    if (column.dataType === 'currency') {
      return `$${calculatedValue.toFixed(2)}`
    } else if (column.dataType === 'percentage') {
      return `${calculatedValue.toFixed(2)}%`
    } else if (column.dataType === 'number') {
      return calculatedValue.toString()
    }

    return calculatedValue.toString()
  }

  const firstColumnTemplate = (rowInfo) => {
    const { rowIndex } = rowInfo

    const dropdownValue = dropdownOptions.find((option) => option?.primarySearchField === dropdownNames?.[rowIndex])

    return (
      <>
        <Dropdown
          value={dropdownValue}
          options={dropdownOptions}
          itemTemplate={(option) => (metadata?.isSpreadsheet ? option.primarySearchField : FormSubmissionOptionTemplate(option))}
          onChange={(e) => onDropdownChange(e, rowIndex)}
          optionLabel={metadata?.isSpreadsheet ? 'primarySearchField' : 'primarySearchFieldLabel'}
          filter
          disabled={disabled}
        />
      </>
    )
  }

  const onDropdownChange = (e, index) => {
    function transformObject(input) {
      const result = {
        [input.id]: input.primarySearchField,
        primarySearchField: input.primarySearchField
      }

      input.resultFieldsData.forEach((field) => {
        result[field.key] = field.value
      })

      return result
    }

    setInputs((prevInputs) => {
      const currentRows = prevInputs[name] || []
      let newRows = [...currentRows]

      const newValue = transformObject(e.value)

      newRows[index] = {
        ...newRows[index],
        ...newValue
      }

      // Calculate values for any calculated columns
      dataTableColumnsFromMetadata?.forEach((column) => {
        if (column.hasCalculate && column.calculationFormula) {
          const calculatedValue = calculateValue(column.calculationFormula, newRows[index])
          if (calculatedValue !== null) {
            const fieldName = `${column.title}_${index}`
            newRows[index][fieldName] = calculatedValue
          }
        }
      })

      return {
        ...prevInputs,
        [name]: newRows
      }
    })

    setDropdownNames((prev) => {
      const newArray = [...prev]
      newArray[index] = e.value.primarySearchField
      return newArray
    })
  }

  const onTemplateChange = (e, index, field) => {
    setInputs((prevInputs) => {
      const currentRows = prevInputs[name] || []
      let newRows = [...currentRows]

      // Ensure the row exists at the given index
      if (!newRows[index]) {
        newRows[index] = {}
      }
      newRows[index][field] = e.target?.value ? e.target.value : e.value ? e.value : ''

      // Calculate values for any calculated columns
      dataTableColumns?.forEach((column) => {
        if (column.hasCalculate && column.calculationFormula) {
          const calculatedValue = calculateValue(column.calculationFormula, newRows[index])
          if (calculatedValue !== null) {
            const fieldName = `${column.title}_${index}`
            newRows[index][fieldName] = calculatedValue
          }
        }
      })

      const data = {
        ...prevInputs,
        [name]: newRows
      }
    })
  }

  const onAddRow = () => {
    setInputs((prevInputs) => {
      const currentRows = prevInputs[name] || []
      let newRows = [...currentRows]

      // If we have no rows or only an empty row, start fresh with two empty rows
      if (currentRows.length === 0 || (currentRows.length === 1 && Object.keys(currentRows[0]).length === 0)) {
        newRows = [{}, {}]
      } else {
        newRows.push({})
      }

      return {
        ...prevInputs,
        [name]: newRows
      }
    })
  }

  const onAddBatchRows = () => {
    const combinedLineItems = combineLineItems(selectedLineItems, lineItemsBatch)

    setInputs((prevInputs) => {
      const currentRows = prevInputs[name] ? JSON.parse(JSON.stringify(prevInputs[name])) : []

      const newRows = [...currentRows, ...combinedLineItems]

      return {
        ...prevInputs,
        [name]: newRows
      }
    })

    setDropdownNames((prev) => {
      const newArray = [...prev]

      combinedLineItems.map((lineItem) => newArray.push(lineItem.primarySearchField))

      return newArray
    })

    setSelectedLineItems([])
    setLineItemsBatch([])
  }

  const onDeleteRow = (rowIndex) => {
    if (disabled) return

    setInputs((prevInputs) => {
      const currentRows = prevInputs[name] || []
      let newRows = [...currentRows]
      const [deletedRow] = newRows.splice(rowIndex, 1)

      if (deletedRow) {
        setDropdownNames((prevNames) => {
          const deletedName = deletedRow.primarySearchField
          return prevNames.filter((prevName, index) => {
            return !(prevName === deletedName && index === rowIndex)
          })
        })
      }

      return {
        ...prevInputs,
        [name]: newRows
      }
    })
  }

  const handleLineItemChange = (e, index, field) => {
    setLineItemsBatch((prevBatch) => {
      const newBatch = [...prevBatch]

      newBatch[index] = {
        ...newBatch[index],
        [field]: e.target?.value ? e.target.value : e.value ? e.value : ''
      }

      return newBatch
    })
  }

  const combineLineItems = (existingItems, batchItems) => {
    const batchProperties = Object.assign({}, ...batchItems)

    return existingItems.map((item) => ({
      ...item,
      ...batchProperties
    }))
  }

  const dateDisplayTemplate = (rowData, field) => {
    if (rowData[field] && (Object.prototype.toString.call(rowData[field]) === '[object Date]' || !isNaN(new Date(rowData[field])))) {
      return createDashboardDate(rowData[field])
    } else {
      return ''
    }
  }

  const textTemplate = (field, rowInfo) => {
    const { rowIndex } = rowInfo

    return (
      <InputText
        value={value?.[rowIndex]?.[fieldKey]}
        onChange={(e) => onTemplateChange(e, rowIndex, fieldKey)}
        disabled={disabled}
        style={{ padding: 0 }}
        inputClassName={'pl-1 p-0'}
      />
    )
  }

  const numberTemplate = (field, rowInfo) => {
    const { rowIndex } = rowInfo
    const rawValue = value?.[rowIndex]?.[field]
    const numValue = typeof rawValue === 'object' && rawValue?.$numberInt ? parseInt(rawValue.$numberInt) : rawValue

    return (
      <InputNumber
        // type="number"
        value={numValue}
        onChange={(e) => onTemplateChange(e, rowIndex, fieldKey)}
        disabled={disabled}
        style={{ paddingLeft: 0, paddingRight: 0 }}
        inputClassName={'pl-1 p-0'}
      />
    )
  }

  const currencyTemplate = (field, rowInfo) => {
    const { rowIndex } = rowInfo
    const rawValue = value?.[rowIndex]?.[field]
    const numValue = typeof rawValue === 'object' && rawValue?.$numberInt ? parseInt(rawValue.$numberInt) : rawValue

    return (
      <InputNumber
        mode="currency"
        currency="USD"
        value={numValue}
        onChange={(e) => onTemplateChange(e, rowIndex, fieldKey)}
        disabled={disabled}
        style={{ padding: 0 }}
        inputClassName={'pl-1 p-0'}
      />
    )
  }

  const percentageTemplate = (field, rowInfo) => {
    const { rowIndex } = rowInfo
    const rawValue = value?.[rowIndex]?.[field]
    const numValue = typeof rawValue === 'object' && rawValue?.$numberInt ? parseInt(rawValue.$numberInt) : rawValue

    return (
      <InputNumber
        value={numValue}
        onChange={(e) => onTemplateChange(e, rowIndex, fieldKey)}
        suffix="%"
        minFractionDigits={2}
        maxFractionDigits={2}
        disabled={disabled}
        inputClassName={'pl-1 p-0'}
      />
    )
  }

  const selectableDateTemplate = (field, rowInfo) => {
    const { rowIndex } = rowInfo
    const rawValue = value?.[rowIndex]?.[field]
    const dateValue = typeof rawValue === 'string' ? new Date(rawValue) : rawValue

    return <Calendar value={dateValue} onChange={(e) => onTemplateChange(e, rowIndex, fieldKey)} disabled={disabled} />
  }

  const todayDateTemplate = (field, rowInfo) => {
    const { rowIndex } = rowInfo
    const rawValue = value?.[rowIndex]?.[field]
    const dateValue = typeof rawValue === 'string' ? new Date(rawValue) : rawValue

    return <Calendar value={dateValue} onChange={(e) => onTemplateChange(e, rowIndex, fieldKey)} disabled={disabled} />
  }

  const dropdownTemplate = (column, rowInfo) => {
    const { rowIndex } = rowInfo
    const { field, dropdownOptions } = column

    return (
      <Dropdown
        value={value?.[rowIndex]?.[field]}
        onChange={(e) => onTemplateChange(e, rowIndex, fieldKey)}
        options={dropdownOptions}
        disabled={disabled}
      />
    )
  }
  const checkboxTemplate = (column, rowInfo) => {
    const { rowIndex } = rowInfo
    const { field, dropdownOptions } = column
    const fieldKey = getFieldKey(field)

    console.log('checkboxTemplate', column, rowInfo, value, value?.[rowIndex]?.[fieldKey])
    return (
      <Checkbox
        checked={value?.[rowIndex]?.[fieldKey]}
        onChange={(e) => onTemplateChange(e, rowIndex, fieldKey)}
        options={dropdownOptions}
        disabled={disabled}
      />
    )
  }

  const multiselectTemplate = (column, rowInfo) => {
    const { rowIndex } = rowInfo
    const { field, dropdownOptions } = column

    return (
      <MultiSelect
        value={value?.[rowIndex]?.[field]}
        onChange={(e) => onTemplateChange(e, rowIndex, fieldKey)}
        options={dropdownOptions}
        disabled={disabled}
      />
    )
  }

  const deleteTemplate = (rowIndex) => {
    // Don't show delete button for empty rows
    const rowData = value?.[rowIndex] || {}
    if (Object.keys(rowData).length === 0 && rowIndex === 0) return null

    return (
      <i className="pi pi-trash" style={{ color: 'red', fontSize: '1.3rem', cursor: 'pointer' }} onClick={() => onDeleteRow(rowIndex)} />
    )
  }

  const cellEditor = (options) => {
    // Don't allow editing for footer rows
    if (options.rowIndex >= (value?.length || 0) || disabled) return null

    if (options.field.includes('text') || options.field.includes('mask')) {
      const rowInfo = { rowIndex: options.rowIndex }
      return textTemplate(options.field, rowInfo)
    }

    if (options.field.includes('number')) {
      const rowInfo = { rowIndex: options.rowIndex }
      return numberTemplate(options.field, rowInfo)
    }

    if (options.field.includes('calendar')) {
      const rowInfo = { rowIndex: options.rowIndex }
      return selectableDateTemplate(options.field, rowInfo)
    }

    const rowInfo = { rowIndex: options.rowIndex }

    return textTemplate(options.field, rowInfo)
  }

  useEffect(() => {
    const lazyLoadFunction = async () => {
      const extractFormSubmissionData = (apiResponse) => {
        if (!apiResponse?.data?.formSubmissions) {
          return []
        }

        return apiResponse.data.formSubmissions.map((submission) => submission.data)
      }

      const addPrimarySearchField = (formSubmissionData, primarySearchField) =>
        formSubmissionData.map((item) => ({
          ...item,
          primarySearchField: item[primarySearchField.name] || ''
        }))

      const accessToken = await (await acquireToken()).accessToken
      const config = {
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${accessToken}`
        }
      }

      const formSubmissionData = extractFormSubmissionData(
        await getFormSubmissionsFiltered(sourceFormData.id, lazyParamsToQueryString(lazyParams))
      )
      const updatedFormSubData = addPrimarySearchField(formSubmissionData, primarySearchField)

      setFormSubmissionData(updatedFormSubData)
    }

    lazyLoadFunction()
  }, [])

  useEffect(() => {
    const { name } = primarySearchField

    setDropdownOptions(
      formSubmissionData.map((submission, index) => {
        const temp = JSON.parse(JSON.stringify(submission))

        // This is needed to use the ConcatenateValues function from ObjectLinkDropdown.js
        const resultFieldsData = metadata.resultFields.map((resultField) => {
          return {
            key: resultField?.id,
            value: formSubmissionData[index][resultField?.id]
          }
        })

        return {
          primarySearchField: temp[name],
          resultFieldsData: resultFieldsData,
          id: name,
          primarySearchFieldLabel: primarySearchField.label,
          resultFieldLabels: metadata.resultFields.map((resultField) => resultField.name)
        }
      })
    )
  }, [formSubmissionData])

  useEffect(() => {
    if (!value || !additionalColumns || value === '●●●●●●●●') return

    const newValue = [...value]
    let hasChanges = false

    additionalColumns.forEach((column, columnIndex) => {
      if (!column.hasCalculate || !column.calculationFormula) return

      value.forEach((rowData, rowIndex) => {
        const calculatedValue = calculateValue(column.calculationFormula, rowData)
        const fieldName = `${column.title}_${rowIndex}`

        if (calculatedValue !== null && calculatedValue !== rowData[fieldName]) {
          if (!newValue[rowIndex]) {
            newValue[rowIndex] = {}
          }
          newValue[rowIndex][fieldName] = calculatedValue
          hasChanges = true
        }
      })
    })

    if (hasChanges) {
      setInputs((prevInputs) => ({
        ...prevInputs,
        [name]: newValue
      }))
    }
  }, [
    value === '●●●●●●●●'
      ? null
      : value?.map((row) => {
          // Create a dependency array that only includes the fields used in calculations
          const relevantFields = {}
          additionalColumns?.forEach((column) => {
            if (column.hasCalculate && column.calculationFormula) {
              // Extract column names from the formula
              const columnNames = column.calculationFormula
                .split(/[\+\-\*\/\(\)]/)
                .map((part) => part.trim())
                .filter((part) => part && !/^\d+$/.test(part))

              columnNames.forEach((colName) => {
                const fieldName = additionalColumns.find((col) => col.header === colName)?.field
                if (fieldName && row[fieldName] !== undefined) {
                  relevantFields[fieldName] = row[fieldName]
                }
              })
            }
          })
          return JSON.stringify(relevantFields)
        }),
    name,
    setInputs
  ])

  return (
    <ComponentContainer>
      <Modal visible={isLineItemsModalVisible} onHide={() => setIsLineItemsModalVisible(false)} header={'Add Line Items'} width={'70'}>
        <div style={{ display: 'flex', flexDirection: 'column', gap: '1rem' }}>
          {additionalColumns?.map((column, index) => (
            <div key={`${column.title}_${index}`}>
              <label
                style={{
                  display: 'block',
                  marginBottom: '0.5rem',
                  fontWeight: 'bold',
                  color: 'black'
                }}
              >
                {column.title} {column.isRequired && <span style={{ color: 'red' }}>*</span>}
              </label>
              {column.dataType === 'text' && (
                <InputText
                  value={lineItemsBatch[index]?.[`${column.title}_${index}`] || ''}
                  onChange={(e) => handleLineItemChange(e, index, `${column.title}`)}
                  className={viewStyles.maxWidth}
                />
              )}
              {column.dataType === 'number' && (
                <InputNumber
                  value={lineItemsBatch[index]?.[`${column.title}_${index}`] || ''}
                  onChange={(e) => handleLineItemChange(e, index, `${column.title}`)}
                  className={viewStyles.maxWidth}
                />
              )}
              {(column.dataType === 'currency' || column.dataType === 'percentage') && (
                <InputNumber
                  mode="currency"
                  currency="USD"
                  value={lineItemsBatch[index]?.[`${column.title}_${index}`] || ''}
                  onChange={(e) => handleLineItemChange(e, index, `${column.title}`)}
                  className={viewStyles.maxWidth}
                  disabled={column?.hasCalculate}
                  placeholder={column?.hasCalculate ? 'Calculated columns are read only' : ''}
                />
              )}
              {(column.dataType === 'selectableDate' || column.dataType === 'todayDate') && (
                <Calendar
                  value={lineItemsBatch[index]?.[`${column.title}_${index}`] || ''}
                  onChange={(e) => handleLineItemChange(e, index, `${column.title}`)}
                  className={viewStyles.maxWidth}
                />
              )}
              {column.dataType === 'dropdown' && (
                <Dropdown
                  value={lineItemsBatch[index]?.[`${column.title}`] || []}
                  onChange={(e) => handleLineItemChange(e, index, `${column.title}`)}
                  options={column?.dropdownOptions}
                  className={viewStyles.maxWidth}
                />
              )}
              {column.dataType === 'multiselect' && (
                <MultiSelect
                  value={lineItemsBatch[index]?.[`${column.title}`] || []}
                  onChange={(e) => handleLineItemChange(e, index, `${column.title}`)}
                  options={column?.dropdownOptions}
                  className={viewStyles.maxWidth}
                />
              )}
            </div>
          ))}
          <DataTable value={formSubmissionData} selection={selectedLineItems} onSelectionChange={(e) => setSelectedLineItems(e.value)}>
            <Column selectionMode="multiple" />
            {mapFields(resultFields, primarySearchField, null).map((column, index) => (
              <Column key={`${column.field}`} field={column.field} header={column.header} />
            ))}
          </DataTable>
          <div
            style={{
              display: 'flex',
              justifyContent: 'flex-end',
              gap: '1rem',
              marginTop: '1rem'
            }}
          >
            <SecondaryButton text="Cancel" onClick={() => setIsLineItemsModalVisible(false)} />
            <PrimaryButton
              text="Add"
              onClick={() => {
                onAddBatchRows()
                setIsLineItemsModalVisible(false)
              }}
            />
          </div>
        </div>
      </Modal>
      <AlignmentContainer alignment={alignment}>
        <div style={{ display: 'flex', margin: '10px 0' }}>
          <LabelContainer alignment={alignment}>
            <Label label={label} validations={validations} />
          </LabelContainer>
          <div style={{ marginRight: '10px' }}>
            <SecondaryButton
              text="+ Add Line Items"
              onClick={() => setIsLineItemsModalVisible(true)}
              disabled={disabled || value === '●●●●●●●●'}
            />
          </div>
        </div>
        {value?.length > 0 && console.log('dataTableColumns', [...value, ...footerValue])}
        <InputsContainer divClassName={divClassName}>
          {dataTableColumns.length > 0 ? (
            <div>
              <DataTable
                value={
                  value === '●●●●●●●●'
                    ? Array(5)
                        .fill({})
                        .map(() => Object.fromEntries(dataTableColumns.map((col) => [col.field, '●●●●●●●●'])))
                    : value?.length > 0
                    ? [...value, ...calculateFooterValues()]
                    : [{}, ...calculateFooterValues()]
                    ? [...value, ...calculateFooterValues()]
                    : [{}, ...calculateFooterValues()]
                }
                editMode="cell"
                resizableColumns
                showGridlines
              >
                {dataTableColumns.map((column, index) => (
                  <Column
                    // sortable <- Doesn't work. Need to fix.
                    key={`${column.field}-${index}`}
                    field={column.field}
                    header={
                      <div
                        style={{
                          display: 'flex',
                          alignItems: 'center',
                          gap: '4px'
                        }}
                      >
                        {column.isRequired && <span style={{ color: 'red' }}>*</span>}
                        {column.header}
                      </div>
                    }
                    body={(rowData, rowInfo) => {
                      // For masked display, always return dots
                      if (value === '●●●●●●●●') {
                        return <span>{'●●●●●●●●'}</span>
                      }

                      // For footer rows, display the value directly
                      if (rowInfo.rowIndex >= (value?.length || 1)) {
                        const footerValue = rowData[column.field]
                        // If it's a number, format it according to the column's dataType
                        if (typeof footerValue === 'number') {
                          if (column.dataType === 'currency') {
                            return `$${footerValue.toFixed(2)}`
                          } else if (column.dataType === 'percentage') {
                            return `${footerValue.toFixed(2)}%`
                          }
                          return footerValue.toString()
                        }
                        return footerValue || ''
                      }

                      // Regular row handling
                      if (index === 0) {
                        return firstColumnTemplate(rowInfo)
                      }
                      if (column.field.includes('calendar') && !column?.dataType) {
                        return dateDisplayTemplate(rowData, column.field)
                      }
                      if (column?.hasCalculate && column?.calculationFormula) {
                        return calculatedTemplate(column, rowInfo)
                      }
                      // If the cell is editable, use the appropriate template
                      if (column?.editable || !column?.hasCalculate) {
                        switch (column?.dataType) {
                          case 'text':
                            return textTemplate(column.field, rowInfo)
                          case 'number':
                            return numberTemplate(column.field, rowInfo)
                          case 'currency':
                            return currencyTemplate(column.field, rowInfo)
                          case 'percentage':
                            return percentageTemplate(column.field, rowInfo)
                          case 'selectableDate':
                            return selectableDateTemplate(column.field, rowInfo)
                          case 'todayDate':
                            return todayDateTemplate(column.field, rowInfo)
                          case 'dropdown':
                            return dropdownTemplate(column, rowInfo)
                          case 'multiselect':
                            return multiselectTemplate(column, rowInfo)
                          default:
                            return textTemplate(column.field, rowInfo)
                        }
                      } else {
                        // For non-editable cells, return the value as a string
                        const cellValue = value?.[rowInfo?.rowIndex]?.[column.field]
                        if (cellValue === undefined || cellValue === null) return ''

                        // Format based on data type
                        switch (column?.dataType) {
                          case 'currency':
                            return `$${parseFloat(cellValue).toFixed(2)}`
                          case 'percentage':
                            return `${parseFloat(cellValue).toFixed(2)}%`
                          case 'number':
                            return parseFloat(cellValue).toString()
                          case 'selectableDate':
                          case 'todayDate':
                            return dateDisplayTemplate(rowData, column.field)
                          default:
                            return cellValue.toString()
                        }
                      }
                    }}
                    editor={column?.editable && value !== '●●●●●●●●' ? (options) => cellEditor(options) : null}
                  />
                ))}
                <Column
                  header={'Delete'}
                  body={(_, rowInfo) => {
                    // Don't show delete button for footer rows or masked display
                    if (rowInfo.rowIndex >= (value?.length || 0) || value === '●●●●●●●●') return null
                    return deleteTemplate(rowInfo?.rowIndex)
                  }}
                />
              </DataTable>
              <SecondaryButton
                text="+ Add New Row"
                onClick={onAddRow}
                style={{ marginTop: '10px' }}
                disabled={disabled || value === '●●●●●●●●'}
              />
            </div>
          ) : (
            'Please Configure Component in Settings'
          )}
          <Subtitle subtitle={subtitle} />
        </InputsContainer>
      </AlignmentContainer>
    </ComponentContainer>
  )
}

export function ViewGrid({ metadata, setInputs, value: valueProp, inputs, wholeMetadata }) {
  const value = valueProp?.bodyData ?? null
  // TODO: Consider splitting this large component into smaller, focused components:
  // 1. FooterCalculator - Handles all footer field calculations
  // 2. RowManager - Handles row operations (add, delete, batch)
  // 3. ColumnRenderer - Handles different column type templates
  // 4. DataTableWrapper - Handles DataTable configuration and rendering
  // 5. FormulaEvaluator - Handles formula calculations

  // TODO: Consider using React Context for shared state between components
  // This would reduce prop drilling and make state management clearer

  // TODO: Extract all template functions into a separate file
  // This would make the component more focused and easier to maintain

  // TODO: Consider using a reducer pattern for complex state management
  // This would make state updates more predictable and easier to debug

  // TODO: Add proper TypeScript interfaces for props and state
  // This would improve type safety and developer experience

  // TODO: Consider using custom hooks for:
  // - Form submission data fetching
  // - Dropdown options management
  // - Line items batch processing
  // - Footer calculations

  const { disabled } = metadata

  const extractPrimarySearchFields = (items) => {
    console.log('extractPrimarySearchFields', items)
    if (!items || items === '●●●●●●●●') return []

    return items.map((item) => item.primarySearchField)
  }

  //   // If value is masked, don't return any footer values
  //   if (value === "●●●●●●●●") return [];

  //   if (!metadata?.footerFields) return [];
  //   // Get all unique column names that have footer fields
  //   const columnNames = Object.keys(metadata.footerFields);

  //   if (columnNames.length === 0) return [];

  //   // Find the maximum number of footer rows needed
  //   const maxFooterRows = Math.max(
  //     ...columnNames.map((col) => metadata.footerFields[col].length)
  //   );

  //   // Create footer rows
  //   const footerRows = Array(maxFooterRows)
  //     .fill({})
  //     .map((_, rowIndex) => {
  //       const row = {};
  //       columnNames.forEach((col) => {
  //         const footerField = metadata.footerFields[col][rowIndex];
  //         if (footerField) {
  //           // Find the corresponding column in dataTableColumns
  //           const column = dataTableColumns.find((c) => c.header === col);
  //           if (!column) return;

  //           if (footerField.type === "calculation") {
  //             // For calculations, we need to evaluate the formula
  //             const formula = footerField.value;

  //             const functionMatch = formula.match(/^([A-Za-z]+)\((.*)\)$/);
  //             console.log("functionMatch", functionMatch);
  //             if (functionMatch) {
  //               const [, functionName, args] = functionMatch;

  //               // Handle range syntax [start:end]
  //               const rangeMatch = args.match(/\[(\d+|end):(\d+|end)\]$/);
  //               let columnName = args;
  //               let range = null;

  //               if (rangeMatch) {
  //                 columnName = args.replace(/\[.*\]/, "").trim();
  //                 const start =
  //                   rangeMatch[1] === "end"
  //                     ? value?.length
  //                     : parseInt(rangeMatch[1]);
  //                 const end =
  //                   rangeMatch[2] === "end"
  //                     ? value?.length
  //                     : parseInt(rangeMatch[2]);
  //                 range = {
  //                   start: start,
  //                   end: end,
  //                 };
  //               }

  //               // Get all values from the rows
  //               const values = [];

  //               if (value && value.length > 0) {
  //                 // For each row in the value array
  //                 value.forEach((rowData, rowIndex) => {
  //                   // Find all keys that match the column name pattern
  //                   const matchingColumn = dataTableColumns.find(
  //                     (c) => c.header === columnName
  //                   );
  //                   if (!matchingColumn) {
  //                     return;
  //                   }

  //                   const matchingKeys = Object.keys(rowData).filter((key) => {
  //                     // For additional columns, check if key starts with column name
  //                     if (matchingColumn.field.startsWith(`${columnName}_`)) {
  //                       return key.startsWith(columnName + "_");
  //                     }
  //                     // For result fields, check if key matches the field GUID
  //                     return key === matchingColumn.field;
  //                   });
  //                   console.log("matchingColumn", rowData, matchingKeys);

  //                   matchingKeys.forEach((key) => {
  //                     const val = rowData[key];
  //                     if (val !== undefined && val !== null) {
  //                       if (range) {
  //                         if (
  //                           rowIndex >= range.start - 1 &&
  //                           rowIndex <= range.end - 1
  //                         ) {
  //                           const num = parseFloat(val);
  //                           if (!isNaN(num)) {
  //                             values.push(num);
  //                           }
  //                         }
  //                       } else {
  //                         const num = parseFloat(val);
  //                         if (!isNaN(num)) {
  //                           values.push(num);
  //                         }
  //                       }
  //                     }
  //                   });
  //                 });
  //               }

  //               let result = 0;
  //               switch (functionName.toUpperCase()) {
  //                 case "SUM":
  //                   result = values.reduce((a, b) => a + b, 0);
  //                   break;
  //                 case "AVG":
  //                   result =
  //                     values.length > 0
  //                       ? values.reduce((a, b) => a + b, 0) / values.length
  //                       : 0;
  //                   break;
  //                 case "COUNT":
  //                   result = values.length;
  //                   break;
  //                 case "PRODUCT":
  //                   result =
  //                     values.length > 0 ? values.reduce((a, b) => a * b, 1) : 0;
  //                   break;
  //               }

  //               row[column.field] = result;
  //             }
  //           } else {
  //             // For titles, just use the value directly
  //             row[column.field] = footerField.value;
  //           }
  //         }
  //       });

  //       return row;
  //     });
  //   console.log("maxFooterRows", footerRows);
  //   return footerRows;
  // };

  const calculateFooterValues = () => {
    // If value is masked, don't return any footer values
    if (value === '●●●●●●●●') return []
    if (!metadata?.footerFields) return []

    const columnNames = Object.keys(metadata.footerFields)
    if (columnNames.length === 0) return []

    const maxFooterRows = Math.max(...columnNames.map((col) => metadata.footerFields[col].length))

    const calculatedFooterValues = {}

    // This is the new helper function that contains the main calculation logic
    const evaluateComplexFormula = (formula, call = 1) => {
      // Regex to find all function calls like SUM(...), AVG(...), etc.
      const functionCallRegex = /[A-Za-z]+\([^)]+\)/g
      let processedFormula = formula

      // Find all function calls in the formula string
      const functionCalls = formula.match(functionCallRegex)

      if (functionCalls) {
        functionCalls.forEach((funcCall) => {
          // Now, parse the individual function call (e.g., "SUM(Amount)")
          const functionMatch = funcCall.match(/^([A-Za-z]+)\((.*)\)$/)
          if (!functionMatch) return

          const [, functionName, args] = functionMatch
          let columnName = args.trim()
          let range = null

          // Handle range syntax [start:end], same as your original code
          const rangeMatch = args.match(/\[(\d+|end):(\d+|end)\]$/)
          if (rangeMatch) {
            columnName = args.replace(/\[.*\]/, '').trim()
            const start = rangeMatch[1] === 'end' ? value?.length : parseInt(rangeMatch[1])
            const end = rangeMatch[2] === 'end' ? value?.length : parseInt(rangeMatch[2])
            range = { start: start, end: end }
          }

          // Get all values from the specified column
          const values = []
          if (value && value.length > 0) {
            value.forEach((rowData, rowIndex) => {
              const matchingColumn = dataTableColumns.find((c) => c.header === columnName)
              if (!matchingColumn) return

              const dataValue = rowData[matchingColumn.field ? `${matchingColumn?.field}` : columnName]
              console.log('functionCalls', functionName, args, dataValue, rowData, matchingColumn?.field)

              if (dataValue !== undefined && dataValue !== null) {
                const num = parseFloat(dataValue)
                if (isNaN(num)) return

                // Apply range filter if it exists
                if (range) {
                  if (rowIndex >= range.start - 1 && rowIndex <= range.end - 1) {
                    values.push(num)
                  }
                } else {
                  values.push(num)
                }
              }
            })
          }

          // Calculate the result of this single function
          let result = 0
          switch (functionName.toUpperCase()) {
            case 'SUM':
              result = values.reduce((a, b) => a + b, 0)
              break
            case 'AVG':
              result = values.length > 0 ? values.reduce((a, b) => a + b, 0) / values.length : 0
              break
            case 'COUNT':
              result = values.length
              break
            case 'PRODUCT':
              result = values.length > 0 ? values.reduce((a, b) => a * b, 1) : 0
              break
          }

          // Replace the function call string with its calculated value
          processedFormula = processedFormula.replace(funcCall, result)
        })
      }

      // After replacements, evaluate the final arithmetic expression
      // Example: "150.5 + 25.2" becomes 175.7

      // add a footer reference regex to replace footer references
      const footerReferenceRegex = /@footer_([a-zA-Z0-9]+)_(\d+)/g

      processedFormula = processedFormula.replace(footerReferenceRegex, (match, columnName, id) => {
        const footerId = `footer_${columnName}_${id}`
        console.log('match test', match, columnName, id, calculatedFooterValues)

        return calculatedFooterValues[footerId] !== undefined ? calculatedFooterValues[footerId] : 0 // Default to 0 or handle as error
      })

      console.log(`processedFormula ${call}`, processedFormula, calculatedFooterValues)
      try {
        // Using Function constructor is safer than eval()
        return new Function('return ' + processedFormula)()
      } catch (error) {
        console.error('Error evaluating footer formula:', error)
        return 'Error' // Or some other error indicator
      }
    }

    // Create footer rows
    const footerRows = Array(maxFooterRows)
      .fill({})
      .map((_, rowIndex) => {
        const row = {}
        columnNames.forEach((col) => {
          const footerField = metadata.footerFields[col][rowIndex]
          if (footerField) {
            const column = dataTableColumns.find((c) => c.header === col)
            if (!column) return

            if (footerField.type === 'calculation') {
              // MODIFIED PART: Call the new evaluator
              const calculatedValue = evaluateComplexFormula(footerField.value, 2)
              row[column.field] = calculatedValue
              console.log('footercell', calculatedValue, footerField, calculatedValue, footerField.value)
              // Store the calculated value if an ID is provided for later reference
              if (footerField.id) {
                calculatedFooterValues[footerField.id] = calculatedValue
              }
            } else {
              // For titles, just use the value directly
              row[column.field] = footerField.value
            }
          }
        })
        return row
      })
    console.log('foot', footerRows)
    return footerRows
  }

  const [formSubmissionData, setFormSubmissionData] = useState([])
  const [dropdownOptions, setDropdownOptions] = useState([])
  const [dropdownNames, setDropdownNames] = useState(extractPrimarySearchFields(value))
  const [isLineItemsModalVisible, setIsLineItemsModalVisible] = useState(false)
  const [lineItemsBatch, setLineItemsBatch] = useState([])
  const [selectedLineItems, setSelectedLineItems] = useState([])
  const {
    label,
    name,
    guid,
    subtitle,
    divClassName,
    alignment,
    validations,
    resultFields,
    primarySearchField,
    additionalColumns,
    dataTableColumns: dataTableColumnsFromMetadata,
    sourceFormData
  } = metadata
  const { createDashboardDate } = useUtilityFunctions()
  const dataTableColumns = mapFields([], primarySearchField, dataTableColumnsFromMetadata, inputs)

  const footerValue = calculateFooterValues()

  const { callApi } = useApi()

  const initialLazyParams = {
    first: 0,
    page: 0,
    rows: 50,
    sortField: 'lastUpdatedAtUtc',
    sortOrder: -1
  }
  const { lazyParamsToQueryString, lazyParams } = useDashboard(initialLazyParams)

  function mapFields(resultFields, primarySearchField, additionalColumns, inputs = undefined) {
    if (!resultFields || !primarySearchField) return []
    const mappedFields = []

    console.log('mapFields', metadata, inputs, resultFields, primarySearchField)
    // Add primary search field first
    if (primarySearchField?.name && primarySearchField?.label) {
      mappedFields.push({
        field: primarySearchField.name,
        header: metadata?.primaryFieldDisplayName
          ? metadata.primaryFieldDisplayName
          : (primarySearchField?.displayName || primarySearchField?.label) ?? ''
      })
    }

    // Map each result field
    resultFields.forEach((field) => {
      if (field?.id && field?.name) {
        mappedFields.push({
          field: field.id,
          header: field.name,
          dataType: field?.dataType,
          editable: field?.editable ?? false
        })
      }
    })

    // Map additional columns
    if (additionalColumns) {
      additionalColumns.forEach((column, index) => {
        if (column.title) {
          mappedFields.push({
            field: `${column.id ?? column?.title}`,
            header: column.title,
            dataType: column.dataType,
            dropdownOptions: column?.dropdownOptions ?? [],
            calculationFormula: column?.calculationFormula,
            hasCalculate: column?.hasCalculate,
            isRequired: column?.isRequired
          })
        }
      })
    }

    return mappedFields
  }

  const calculateValue = (formula, rowData) => {
    if (!formula || !rowData || Object.keys(rowData).length === 0) return null

    try {
      // Create a mapping of column titles to their field names
      const columnMap = {}
      dataTableColumns.forEach((column) => {
        if (column.field && column.header) {
          columnMap[column.header] = {
            field: column.field,
            dataType: column.dataType,
            // Check if the column name contains % or if it's a percentage type
            isPercentage: column.dataType === 'percentage' || column.header.includes('%')
          }
        }
      })
      // First, replace all column names with placeholders to protect them
      const columnPlaceholders = {}
      let placeholderCount = 0
      const formulaWithPlaceholders = Object.keys(columnMap).reduce((acc, columnName) => {
        const placeholder = `__COL${placeholderCount}__`
        columnPlaceholders[placeholder] = columnName
        placeholderCount++
        return acc.replace(new RegExp(columnName.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g'), placeholder)
      }, formula)

      // Replace column placeholders with their values
      const evaluatedFormula = formulaWithPlaceholders.replace(/__COL\d+__/g, (match) => {
        const columnName = columnPlaceholders[match]
        const columnInfo = columnMap[columnName]
        if (!columnInfo) return match

        // Get the value from rowData using the field name
        const value = rowData[columnInfo?.field?.split('_')[0] ?? '']
        console.log('match', rowData, columnInfo, columnMap[columnName], columnMap, columnName, value)
        if (value === undefined) return match

        // Handle different number formats
        let numericValue
        if (typeof value === 'object' && value?.$numberInt) {
          numericValue = parseInt(value.$numberInt)
        } else if (typeof value === 'object' && value?.$numberDecimal) {
          numericValue = parseFloat(value.$numberDecimal)
        } else if (typeof value === 'string') {
          numericValue = parseFloat(value)
        } else {
          numericValue = value
        }

        // Convert percentage to decimal if needed
        if (columnInfo.isPercentage) {
          // Remove any % sign and convert to decimal
          numericValue = parseFloat(numericValue.toString().replace('%', ''))
        }

        return isNaN(numericValue) ? value : numericValue
      })

      // Evaluate the formula
      const result = new Function('return ' + evaluatedFormula)()

      // Ensure the result is a number
      const numericResult = parseFloat(result)
      return isNaN(numericResult) ? null : numericResult
    } catch (error) {
      console.error('Error calculating formula:', error)
      return null
    }
  }

  const calculatedTemplate = (column, rowInfo) => {
    const { rowIndex } = rowInfo
    const rowData = value?.[rowIndex] || {}

    if (!column.calculationFormula) return null

    // Check if rowData is empty
    if (Object.keys(rowData).length === 0) {
      return <span style={{ color: '#666' }}>No data available</span>
    }

    const calculatedValue = calculateValue(column.calculationFormula, rowData)

    // If we got null from calculateValue, it means some required values were missing
    if (calculatedValue === null) {
      // Create a mapping of column titles to their field names
      const columnMap = {}
      dataTableColumns.forEach((col) => {
        if (col.field && col.header) {
          columnMap[col.header] = col.field
        }
      })

      // First, replace all column names with placeholders to protect them
      const columnPlaceholders = {}
      let placeholderCount = 0
      const formulaWithPlaceholders = Object.keys(columnMap).reduce((acc, columnName) => {
        const placeholder = `__COL${placeholderCount}__`
        columnPlaceholders[placeholder] = columnName
        placeholderCount++
        return acc.replace(new RegExp(columnName.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g'), placeholder)
      }, column.calculationFormula)

      // Now split on operators and parentheses to get the parts
      const parts = formulaWithPlaceholders
        .split(/[\+\-\*\/\(\)]/)
        .map((part) => part.trim())
        .filter((part) => part && !/^\d+$/.test(part))

      // Convert placeholders back to column names
      const columnNames = parts.map((part) => {
        if (part in columnPlaceholders) {
          return columnPlaceholders[part]
        }
        return part
      })

      // Check which columns are missing values
      const missingColumns = columnNames.filter((name) => {
        const fieldName = columnMap[name]
        return !rowData[fieldName]
      })

      if (missingColumns.length > 0) {
        return <span style={{ color: '#666' }}>Missing values for: {missingColumns.join(', ')}</span>
      }

      return <span style={{ color: 'red' }}>Invalid calculation</span>
    }

    // Format the calculated value based on the column's dataType
    if (column.dataType === 'currency') {
      return `$${calculatedValue.toFixed(2)}`
    } else if (column.dataType === 'percentage') {
      return `${calculatedValue.toFixed(2)}%`
    } else if (column.dataType === 'number') {
      return calculatedValue.toString()
    }

    return calculatedValue.toString()
  }

  const firstColumnTemplate = (rowInfo) => {
    const { rowIndex } = rowInfo
    const dropdownValue = dropdownOptions.find((option) => option?.primarySearchField === dropdownNames?.[rowIndex])

    console.log('firstColumnTemplate', dropdownValue, dropdownOptions, dropdownNames)

    return (
      <>
        <Dropdown
          value={dropdownValue}
          options={dropdownOptions}
          itemTemplate={(option) => (metadata?.isSpreadsheet ? option.primarySearchField : FormSubmissionOptionTemplate(option))}
          onChange={(e) => onDropdownChange(e, rowIndex)}
          optionLabel={metadata?.isSpreadsheet ? 'primarySearchField' : 'primarySearchFieldLabel'}
          filter
          disabled={disabled}
        />
      </>
    )
  }

  const onDropdownChange = (e, index) => {
    function transformObject(input) {
      const result = {
        [input.id]: input.primarySearchField,
        primarySearchField: input.primarySearchField
      }

      input.resultFieldsData.forEach((field) => {
        result[field.key] = field.value
      })

      return result
    }

    setInputs((prevInputs) => {
      const currentRows = prevInputs[name]?.bodyData || []
      let newRows = [...currentRows]

      const newValue = transformObject(e.value)

      newRows[index] = {
        ...newRows[index],
        ...newValue
      }

      // Calculate values for any calculated columns
      dataTableColumnsFromMetadata?.forEach((column) => {
        if (column.hasCalculate && column.calculationFormula) {
          const calculatedValue = calculateValue(column.calculationFormula, newRows[index])
          if (calculatedValue !== null) {
            const fieldName = `${column.title}`
            newRows[index][fieldName] = calculatedValue
          }
        }
      })

      console.log(
        'onChange',
        {
          ...prevInputs,
          [name]: newRows
        },
        inputs,
        inputs?.[name],
        value
      )

      return {
        ...prevInputs,
        [name]: { bodyData: newRows, footerData: footerValue }
      }
    })

    setDropdownNames((prev) => {
      const newArray = [...prev]
      newArray[index] = e.value.primarySearchField
      return newArray
    })
  }

  const onTemplateChange = (e, index, field) => {
    setInputs((prevInputs) => {
      const currentRows = prevInputs[name]?.bodyData || []
      let newRows = [...currentRows]

      // Ensure the row exists at the given index
      if (!newRows[index]) {
        newRows[index] = {}
      }
      newRows[index][field] = e.target?.value ? e.target.value : e.value ? e.value : ''

      // Calculate values for any calculated columns
      dataTableColumns?.forEach((column) => {
        if (column.hasCalculate && column.calculationFormula) {
          const calculatedValue = calculateValue(column.calculationFormula, newRows[index])
          console.log('rows', column)
          if (calculatedValue !== null) {
            const fieldName = `${column.field}`
            newRows[index][fieldName] = calculatedValue
          }
        }
      })

      const data = {
        ...prevInputs,
        [name]: { bodyData: newRows, footerData: footerValue }
      }
      console.log('onTemplateChange', data, inputs, inputs?.[name], value)
      return data
    })
  }

  const onAddRow = () => {
    setInputs((prevInputs) => {
      const currentRows = prevInputs[name]?.bodyData || []
      let newRows = [...currentRows]

      // If we have no rows or only an empty row, start fresh with two empty rows
      if (currentRows.length === 0 || (currentRows.length === 1 && Object.keys(currentRows[0]).length === 0)) {
        newRows = [{}, {}]
      } else {
        newRows.push({})
      }

      return {
        ...prevInputs,
        [name]: { bodyData: newRows, footerData: footerValue }
      }
    })
  }

  const onAddBatchRows = () => {
    const combinedLineItems = combineLineItems(selectedLineItems, lineItemsBatch)

    setInputs((prevInputs) => {
      const currentRows = prevInputs[name]?.bodyData ? JSON.parse(JSON.stringify(prevInputs[name]?.bodyData)) : []

      const newRows = [...currentRows, ...combinedLineItems]

      return {
        ...prevInputs,
        [name]: { bodyData: newRows, footerData: footerValue }
      }
    })

    setDropdownNames((prev) => {
      const newArray = [...prev]

      combinedLineItems.map((lineItem) => newArray.push(lineItem.primarySearchField))

      return newArray
    })

    setSelectedLineItems([])
    setLineItemsBatch([])
  }

  const onDeleteRow = (rowIndex) => {
    if (disabled) return

    setInputs((prevInputs) => {
      const currentRows = prevInputs[name]?.bodyData || []
      let newRows = [...currentRows]
      const [deletedRow] = newRows.splice(rowIndex, 1)

      if (deletedRow) {
        setDropdownNames((prevNames) => {
          const deletedName = deletedRow.primarySearchField
          return prevNames.filter((prevName, index) => {
            return !(prevName === deletedName && index === rowIndex)
          })
        })
      }

      return {
        ...prevInputs,
        [name]: { bodyData: newRows, footerData: footerValue }
      }
    })
  }

  const handleLineItemChange = (e, index, field) => {
    setLineItemsBatch((prevBatch) => {
      const newBatch = [...prevBatch]

      newBatch[index] = {
        ...newBatch[index],
        [field]: e.target?.value ? e.target.value : e.value ? e.value : ''
      }

      return newBatch
    })
  }

  const combineLineItems = (existingItems, batchItems) => {
    const batchProperties = Object.assign({}, ...batchItems)

    return existingItems.map((item) => ({
      ...item,
      ...batchProperties
    }))
  }

  const dateDisplayTemplate = (rowData, field) => {
    if (rowData[field] && (Object.prototype.toString.call(rowData[field]) === '[object Date]' || !isNaN(new Date(rowData[field])))) {
      return createDashboardDate(rowData[field])
    } else {
      return ''
    }
  }

  function getFieldKey(field) {
    return field ? field.split('.')[0] : undefined
  }

  const textTemplate = (field, rowInfo) => {
    const { rowIndex } = rowInfo
    const fieldKey = getFieldKey(field)
    console.log('textTemplate', field, rowIndex, fieldKey, value, value?.[rowIndex], value?.[rowIndex]?.[fieldKey])
    return (
      <InputText
        value={value?.[rowIndex]?.[fieldKey]}
        onChange={(e) => onTemplateChange(e, rowIndex, fieldKey)}
        disabled={disabled}
        style={{ padding: 0 }}
        inputClassName={'pl-1 p-0'}
      />
    )
  }

  const numberTemplate = (field, rowInfo) => {
    const { rowIndex } = rowInfo
    const fieldKey = getFieldKey(field)
    const rawValue = value?.[rowIndex]?.[fieldKey]
    const numValue = typeof rawValue === 'object' && rawValue?.$numberInt ? parseInt(rawValue.$numberInt) : rawValue

    return (
      <InputNumber
        // type="number"
        value={numValue}
        onChange={(e) => onTemplateChange(e, rowIndex, fieldKey)}
        disabled={disabled}
        style={{ paddingLeft: 0, paddingRight: 0 }}
        inputClassName={'pl-1 p-0'}
      />
    )
  }

  const currencyTemplate = (field, rowInfo) => {
    const { rowIndex } = rowInfo
    const fieldKey = getFieldKey(field)
    const rawValue = value?.[rowIndex]?.[fieldKey]
    const numValue = typeof rawValue === 'object' && rawValue?.$numberInt ? parseInt(rawValue.$numberInt) : rawValue

    return (
      <InputNumber
        mode="currency"
        currency="USD"
        value={numValue}
        onChange={(e) => onTemplateChange(e, rowIndex, fieldKey)}
        disabled={disabled}
        style={{ padding: 0 }}
        inputClassName={'pl-1 p-0'}
      />
    )
  }

  const percentageTemplate = (field, rowInfo) => {
    const { rowIndex } = rowInfo
    const fieldKey = getFieldKey(field)
    const rawValue = value?.[rowIndex]?.[fieldKey]
    const numValue = typeof rawValue === 'object' && rawValue?.$numberInt ? parseInt(rawValue.$numberInt) : rawValue

    return (
      <InputNumber
        value={numValue}
        onChange={(e) => onTemplateChange(e, rowIndex, fieldKey)}
        suffix="%"
        minFractionDigits={2}
        maxFractionDigits={2}
        disabled={disabled}
        inputClassName={'pl-1 p-0'}
      />
    )
  }

  const selectableDateTemplate = (field, rowInfo) => {
    const { rowIndex } = rowInfo
    const fieldKey = getFieldKey(field)
    const rawValue = value?.[rowIndex]?.[fieldKey]
    const dateValue = typeof rawValue === 'string' ? new Date(rawValue) : rawValue

    return <Calendar value={dateValue} onChange={(e) => onTemplateChange(e, rowIndex, fieldKey)} disabled={disabled} />
  }

  const todayDateTemplate = (field, rowInfo) => {
    const { rowIndex } = rowInfo
    const fieldKey = getFieldKey(field)
    const rawValue = value?.[rowIndex]?.[fieldKey]
    const dateValue = typeof rawValue === 'string' ? new Date(rawValue) : rawValue

    return <Calendar value={dateValue} onChange={(e) => onTemplateChange(e, rowIndex, fieldKey)} disabled={disabled} />
  }

  const dropdownTemplate = (column, rowInfo) => {
    const { rowIndex } = rowInfo
    const { field, dropdownOptions } = column
    const fieldKey = getFieldKey(field)

    return (
      <Dropdown
        value={value?.[rowIndex]?.[field]}
        onChange={(e) => onTemplateChange(e, rowIndex, fieldKey)}
        options={dropdownOptions}
        disabled={disabled}
      />
    )
  }
  const checkboxTemplate = (column, rowInfo) => {
    const { rowIndex } = rowInfo
    const { field, dropdownOptions } = column
    const fieldKey = getFieldKey(field)

    console.log('checkboxTemplate', column, rowInfo, value, value?.[rowIndex]?.[fieldKey])
    return (
      <Checkbox
        checked={value?.[rowIndex]?.[fieldKey]}
        onChange={(e) => onTemplateChange(e, rowIndex, fieldKey)}
        options={dropdownOptions}
        disabled={disabled}
      />
    )
  }

  const multiselectTemplate = (column, rowInfo) => {
    const { rowIndex } = rowInfo
    const { field, dropdownOptions } = column
    const fieldKey = getFieldKey(field)

    return (
      <MultiSelect
        value={value?.[rowIndex]?.[field]}
        onChange={(e) => onTemplateChange(e, rowIndex, fieldKey)}
        options={dropdownOptions}
        disabled={disabled}
      />
    )
  }

  const deleteTemplate = (rowIndex) => {
    // Don't show delete button for empty rows
    const rowData = value?.[rowIndex] || {}
    if (Object.keys(rowData).length === 0 && rowIndex === 0) return null

    return (
      <i className="pi pi-trash" style={{ color: 'red', fontSize: '1.3rem', cursor: 'pointer' }} onClick={() => onDeleteRow(rowIndex)} />
    )
  }

  const cellEditor = (options) => {
    // Don't allow editing for footer rows
    if (options.rowIndex >= (value?.length || 0) || disabled) return null

    if (options.field.includes('text') || options.field.includes('mask')) {
      const rowInfo = { rowIndex: options.rowIndex }
      return textTemplate(options.field, rowInfo)
    }

    if (options.field.includes('number')) {
      const rowInfo = { rowIndex: options.rowIndex }
      return numberTemplate(options.field, rowInfo)
    }

    if (options.field.includes('calendar')) {
      const rowInfo = { rowIndex: options.rowIndex }
      return selectableDateTemplate(options.field, rowInfo)
    }

    const rowInfo = { rowIndex: options.rowIndex }

    return textTemplate(options.field, rowInfo)
  }

  const { acquireToken } = useMsalAuthentication(InteractionType.Silent, formBuilderStudioApi)

  useEffect(() => {
    const lazyLoadFunction = async () => {
      const extractFormSubmissionData = (apiResponse) => {
        console.log('formSubmissionDataApi', apiResponse)
        if (metadata?.isSpreadsheet) {
          if (!apiResponse.data.data.result) {
            return []
          }
        } else {
          if (!apiResponse?.data?.formSubmissions) {
            return []
          }
        }

        return metadata?.isSpreadsheet
          ? apiResponse.data.data.result.map((result) => result.itemdata)
          : apiResponse.data.formSubmissions.map((submission) => submission.data)
      }

      const addPrimarySearchField = (formSubmissionData, primarySearchField) =>
        formSubmissionData.map((item) => ({
          ...item,
          primarySearchField: item[primarySearchField.name] || ''
        }))

      const accessToken = await (await acquireToken()).accessToken
      const config = {
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${accessToken}`
        }
      }

      const formSubmissionData = extractFormSubmissionData(
        metadata?.isSpreadsheet
          ? await callApi({
              method: 'GET',
              url: 'PurchaseOrderRequistion/Item?page=-1'
            })
          : await getFormSubmissionsFiltered(sourceFormData.id, lazyParamsToQueryString(lazyParams))
      )

      console.log('formSubmissionData', formSubmissionData)

      const updatedFormSubData = addPrimarySearchField(formSubmissionData, primarySearchField)

      setFormSubmissionData(updatedFormSubData)
    }

    lazyLoadFunction()
  }, [])

  useEffect(() => {
    const { name } = primarySearchField

    setDropdownOptions(
      formSubmissionData.map((submission, index) => {
        const temp = JSON.parse(JSON.stringify(submission))

        // This is needed to use the ConcatenateValues function from ObjectLinkDropdown.js
        const resultFieldsData = metadata.resultFields.map((resultField) => {
          return {
            key: resultField?.id,
            value: formSubmissionData[index][resultField?.id]
          }
        })

        return {
          primarySearchField: temp[name],
          resultFieldsData: resultFieldsData,
          id: name,
          primarySearchFieldLabel: primarySearchField.label,
          resultFieldLabels: metadata.resultFields.map((resultField) => resultField.name)
        }
      })
    )
  }, [formSubmissionData])

  useEffect(() => {
    if (!value || !dataTableColumns || value === '●●●●●●●●') return

    const newValue = [...value]
    let hasChanges = false

    dataTableColumns.forEach((column, columnIndex) => {
      if (!column.hasCalculate || !column.calculationFormula) return

      console.log('Calculating column', column, newValue)

      value.forEach((rowData, rowIndex) => {
        const calculatedValue = calculateValue(column.calculationFormula, rowData)
        const fieldName = `${column?.field ? column.field : column.header}_${rowIndex}`

        if (calculatedValue !== null && calculatedValue !== rowData[fieldName]) {
          if (!newValue[rowIndex]) {
            newValue[rowIndex] = {}
          }
          newValue[rowIndex][fieldName] = calculatedValue
          hasChanges = true
        }
      })
    })

    if (hasChanges) {
      setInputs((prevInputs) => ({
        ...prevInputs,
        [name]: { bodyData: newValue ?? [], footerData: footerValue ?? [] }
      }))
    }
  }, [
    value === '●●●●●●●●'
      ? null
      : value?.map((row) => {
          // Create a dependency array that only includes the fields used in calculations
          const relevantFields = {}
          additionalColumns?.forEach((column) => {
            if (column.hasCalculate && column.calculationFormula) {
              // Extract column names from the formula
              const columnNames = column.calculationFormula
                .split(/[\+\-\*\/\(\)]/)
                .map((part) => part.trim())
                .filter((part) => part && !/^\d+$/.test(part))

              columnNames.forEach((colName) => {
                const fieldName = additionalColumns.find((col) => col.header === colName)?.field
                if (fieldName && row[fieldName] !== undefined) {
                  relevantFields[fieldName] = row[fieldName]
                }
              })
            }
          })
          return JSON.stringify(relevantFields)
        }),
    name,
    setInputs
  ])

  useEffect(() => {
    if (!wholeMetadata) return
    const findAddressfield = Object.values(wholeMetadata).find((field) => field?.label === 'Delivery Address')
    if (!findAddressfield) return
    const name = metadata.name
    const oldInputs = localStorage.getItem('oldInputs')
    console.log('useEffect', findAddressfield, oldInputs, inputs[findAddressfield?.name], inputs)
    if (oldInputs === undefined || oldInputs !== inputs[findAddressfield?.name]) {
      console.count('useEffect called for Delivery Address')
      setInputs((prevInputs) => {
        // console.log("Updating tax rate for Delivery Address", oldInputs,inputs[findAddressfield?.name], oldInputs?.trim() === inputs[findAddressfield?.name].trim());
        const data = {
          ...prevInputs,
          [name]: {
            ...prevInputs[name],
            bodyData: prevInputs[name]?.bodyData?.map((row) => ({
              ...row,
              ['Tax Rate']: inputs[findAddressfield?.name] === '7632 Island Rail Dr North Las Vegas, Nevada(NV), 89084' ? 8.375 : 8.27
            }))
          }
        }
        console.log('Updated inputs', prevInputs, data)
        return data
      })
      localStorage.setItem('oldInputs', inputs[findAddressfield?.name])
    }
  }, [inputs])

  console.log('Rendering DataTable', value, inputs)

  return (
    <ComponentContainer>
      <Modal visible={isLineItemsModalVisible} onHide={() => setIsLineItemsModalVisible(false)} header={'Add Line Items'} width={'70'}>
        <div style={{ display: 'flex', flexDirection: 'column', gap: '1rem' }}>
          {dataTableColumns?.map((column, index) => (
            <div key={`${column.title}`}>
              <label
                style={{
                  display: 'block',
                  marginBottom: '0.5rem',
                  fontWeight: 'bold',
                  color: 'black'
                }}
              >
                {column.title} {column.isRequired && <span style={{ color: 'red' }}>*</span>}
              </label>
              {column.dataType === 'text' && (
                <InputText
                  value={lineItemsBatch[index]?.[`${column.title}`] || ''}
                  onChange={(e) => handleLineItemChange(e, index, `${column.title}`)}
                  className={viewStyles.maxWidth}
                />
              )}
              {column.dataType === 'number' && (
                <InputNumber
                  value={lineItemsBatch[index]?.[`${column.title}`] || ''}
                  onChange={(e) => handleLineItemChange(e, index, `${column.title}`)}
                  className={viewStyles.maxWidth}
                />
              )}
              {(column.dataType === 'currency' || column.dataType === 'percentage') && (
                <InputNumber
                  mode="currency"
                  currency="USD"
                  value={lineItemsBatch[index]?.[`${column.title}`] || ''}
                  onChange={(e) => handleLineItemChange(e, index, `${column.title}`)}
                  className={viewStyles.maxWidth}
                  disabled={column?.hasCalculate}
                  placeholder={column?.hasCalculate ? 'Calculated columns are read only' : ''}
                />
              )}
              {(column.dataType === 'selectableDate' || column.dataType === 'todayDate') && (
                <Calendar
                  value={lineItemsBatch[index]?.[`${column.title}`] || ''}
                  onChange={(e) => handleLineItemChange(e, index, `${column.title}`)}
                  className={viewStyles.maxWidth}
                />
              )}
              {column.dataType === 'dropdown' && (
                <Dropdown
                  value={lineItemsBatch[index]?.[`${column.title}`] || []}
                  onChange={(e) => handleLineItemChange(e, index, `${column.title}`)}
                  options={column?.dropdownOptions}
                  className={viewStyles.maxWidth}
                />
              )}
              {column.dataType === 'multiselect' && (
                <MultiSelect
                  value={lineItemsBatch[index]?.[`${column.title}`] || []}
                  onChange={(e) => handleLineItemChange(e, index, `${column.title}`)}
                  options={column?.dropdownOptions}
                  className={viewStyles.maxWidth}
                />
              )}
            </div>
          ))}
          <DataTable value={formSubmissionData} selection={selectedLineItems} onSelectionChange={(e) => setSelectedLineItems(e.value)}>
            <Column selectionMode="multiple" />
            {mapFields(resultFields, primarySearchField, null).map((column, index) => (
              <Column key={`${column.field}`} field={column.field} header={column.header} />
            ))}
          </DataTable>
          <div
            style={{
              display: 'flex',
              justifyContent: 'flex-end',
              gap: '1rem',
              marginTop: '1rem'
            }}
          >
            <SecondaryButton text="Cancel" onClick={() => setIsLineItemsModalVisible(false)} />
            <PrimaryButton
              text="Add"
              onClick={() => {
                onAddBatchRows()
                setIsLineItemsModalVisible(false)
              }}
            />
          </div>
        </div>
      </Modal>
      <AlignmentContainer alignment={alignment}>
        <div style={{ display: 'flex', margin: '10px 0' }}>
          <LabelContainer alignment={alignment}>
            <Label label={label} validations={validations} />
          </LabelContainer>
          <div style={{ marginRight: '10px' }}>
            <SecondaryButton
              text="+ Add Line Items"
              onClick={() => setIsLineItemsModalVisible(true)}
              disabled={disabled || value === '●●●●●●●●'}
            />
          </div>
        </div>
        {value?.length > 0 && console.log('dataTableColumns', [...value, ...footerValue])}
        <InputsContainer divClassName={divClassName}>
          {dataTableColumns.length > 0 ? (
            <div>
              <DataTable
                value={
                  value === '●●●●●●●●'
                    ? Array(5)
                        .fill({})
                        .map(() => Object.fromEntries(dataTableColumns.map((col) => [col.field, '●●●●●●●●'])))
                    : value?.length > 0
                    ? [...value, ...footerValue]
                    : [{}, ...footerValue]
                }
                editMode="cell"
                resizableColumns
                showGridlines
              >
                {dataTableColumns.map((column, index) => (
                  <Column
                    // sortable <- Doesn't work. Need to fix.
                    key={`${column.field}-${index}`}
                    field={column.field}
                    header={
                      <div
                        style={{
                          display: 'flex',
                          alignItems: 'center',
                          gap: '4px'
                        }}
                      >
                        {column.isRequired && <span style={{ color: 'red' }}>*</span>}
                        {column.header}
                      </div>
                    }
                    body={(rowData, rowInfo) => {
                      // For masked display, always return dots
                      if (value === '●●●●●●●●') {
                        return <span>{'●●●●●●●●'}</span>
                      }

                      // For footer rows, display the value directly
                      if (rowInfo.rowIndex >= (value?.length || 1)) {
                        const footerValue = rowData[column.field]
                        // If it's a number, format it according to the column's dataType
                        if (typeof footerValue === 'number') {
                          if (column.dataType === 'currency') {
                            return `$${footerValue.toFixed(2)}`
                          } else if (column.dataType === 'percentage') {
                            return `${footerValue.toFixed(2)}%`
                          }
                          return footerValue.toString()
                        }
                        return footerValue || ''
                      }

                      // Regular row handling
                      if (index === 0) {
                        // console.log("it's work");
                        return firstColumnTemplate(rowInfo)
                      }
                      if (column.field.includes('calendar') && !column?.dataType) {
                        return dateDisplayTemplate(rowData, column.field)
                      }
                      if (column?.hasCalculate && column?.calculationFormula) {
                        return calculatedTemplate(column, rowInfo)
                      }
                      // If the cell is editable, use the appropriate template
                      if (column?.editable || !column?.hasCalculate) {
                        switch (column?.dataType) {
                          case 'text':
                            return textTemplate(column.field, rowInfo)
                          case 'number':
                            return numberTemplate(column.field, rowInfo)
                          case 'currency':
                            return currencyTemplate(column.field, rowInfo)
                          case 'percentage':
                            return percentageTemplate(column.field, rowInfo)
                          case 'selectableDate':
                            return selectableDateTemplate(column.field, rowInfo)
                          case 'todayDate':
                            return todayDateTemplate(column.field, rowInfo)
                          case 'dropdown':
                            return dropdownTemplate(column, rowInfo)
                          case 'multiselect':
                            return multiselectTemplate(column, rowInfo)
                          case 'checkbox':
                            return checkboxTemplate(column, rowInfo)
                          default:
                            return textTemplate(column.field, rowInfo)
                        }
                      } else {
                        // For non-editable cells, return the value as a string
                        const cellValue = value?.[rowInfo?.rowIndex]?.[column.field]
                        if (cellValue === undefined || cellValue === null) return ''

                        // Format based on data type
                        switch (column?.dataType) {
                          case 'currency':
                            return `$${parseFloat(cellValue).toFixed(2)}`
                          case 'percentage':
                            return `${parseFloat(cellValue).toFixed(2)}%`
                          case 'number':
                            return parseFloat(cellValue).toString()
                          case 'selectableDate':
                          case 'todayDate':
                            return dateDisplayTemplate(rowData, column.field)
                          default:
                            return cellValue.toString()
                        }
                      }
                    }}
                    editor={column?.editable && value !== '●●●●●●●●' ? (options) => cellEditor(options) : null}
                  />
                ))}
                <Column
                  header={'Delete'}
                  body={(_, rowInfo) => {
                    // Don't show delete button for footer rows or masked display
                    if (rowInfo.rowIndex >= (value?.length || 0) || value === '●●●●●●●●') return null
                    return deleteTemplate(rowInfo?.rowIndex)
                  }}
                />
              </DataTable>
              <SecondaryButton
                text="+ Add New Row"
                onClick={onAddRow}
                style={{ marginTop: '10px' }}
                disabled={disabled || value === '●●●●●●●●'}
              />
            </div>
          ) : (
            'Please Configure Component in Settings'
          )}
          <Subtitle subtitle={subtitle} />
        </InputsContainer>
      </AlignmentContainer>
    </ComponentContainer>
  )
}

export const componentMapper = {
  header: ViewHeader,
  text: ViewText,
  calendar: ViewCalendar,
  time: ViewTime,
  number: ViewNumber,
  textarea: ViewTextarea,
  mask: ViewMask,
  dropdown: ViewDropdown,
  multiselect: ViewMultiselect,
  image: ViewImage,
  file: ViewFileInput,
  richText: ViewRichText,
  subtitle: ViewReadonlySubtitle,
  signature: ViewSignature,
  radiobutton: ViewMultiRadioButtons,
  checkbox: ViewCheckbox,
  address: ViewAddress,
  vendorDetails: ViewVendorDetails,
  requestors: ViewRequestors,
  pageBreak: ViewPageBreak,
  termsAndConditions: ViewTermsAndConditions,
  calculatedField: ViewCalculatedField,
  advancedFileUpload: ViewAdvancedFileUpload,
  versionedFileUpload: ViewVersionedFileUpload,
  scale: ViewScaleRating,
  stars: ViewStarRating,
  autoComplete: ViewAutoComplete,
  tableComponent: ViewTableComponent,
  payments: ViewPayments,
  heading: ViewHeading,
  jsTable: ViewJsTableComponent,
  employeeLookup: ViewEmployeeLookup,
  accordion: ViewAccordion,
  userSuggestion: ViewUserSuggestion,
  objectLink: ViewObjectLink,
  objectLinkGrid: ViewObjectLinkGrid,
  grid: ViewGrid
}
