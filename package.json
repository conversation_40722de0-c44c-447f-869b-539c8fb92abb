{"name": "form-builder-studio", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "dev:test": "cross-env NODE_ENV=test next dev", "dev:production": "cross-env NODE_ENV=production next dev", "build": "next build", "build:test": "cross-env NODE_ENV=test next build", "start": "next start", "start:test": "cross-env NODE_ENV=test next start", "lint": "next lint"}, "dependencies": {"@azure/ai-form-recognizer": "^5.0.0", "@azure/msal-browser": "^2.24.0", "@azure/msal-node": "^2.16.2", "@azure/msal-react": "^1.4.0", "@dnd-kit/core": "^6.0.6", "@dnd-kit/sortable": "^7.0.1", "@dnd-kit/utilities": "^3.2.1", "@lexical/react": "^0.31.2", "@microsoft/signalr": "^8.0.7", "@pdftron/webviewer": "10.4.0", "@react-pdf/renderer": "^4.3.0", "@stripe/react-stripe-js": "^2.4.0", "@stripe/stripe-js": "^2.4.0", "@tanstack/react-query": "^4.32.6", "@toast-ui/react-calendar": "^2.1.3", "@twilio/voice-sdk": "^2.13.0", "axios": "^1.6.7", "chart.js": "^4.4.5", "classnames": "^2.3.2", "clsx": "^1.2.1", "cookie": "^1.0.2", "crypto": "^1.0.1", "crypto-js": "^4.1.1", "d3-hierarchy": "^3.1.2", "d3-timer": "^3.0.1", "elkjs": "^0.9.2", "file-saver": "^2.0.5", "final-form": "^4.20.2", "html-react-parser": "^5.2.5", "html2canvas": "^1.4.1", "is-hotkey": "^0.2.0", "js-guid": "^1.0.0", "jsonrepair": "^3.12.0", "jspdf": "^2.5.2", "jspreadsheet-ce": "^4.2.1", "lexical": "^0.31.2", "lodash": "^4.17.21", "mathjs": "^13.2.0", "moment": "^2.29.4", "moment-timezone": "^0.5.43", "next": "^13.4.7", "node-fetch": "^3.2.6", "openai": "^4.93.0", "papaparse": "^5.4.1", "pdfjs-dist": "4.8.69", "primeflex": "^3.3.0", "primeicons": "^6.0.1", "primereact": "^10.8.4", "re-resizable": "6.9.9", "react": "^18.2.0", "react-calendly": "^4.3.1", "react-chartjs-2": "^5.2.0", "react-d3-tree": "^3.6.1", "react-dom": "^18.2.0", "react-final-form": "^6.5.3", "react-pdf": "^9.2.1", "react-schedule-meeting": "^4.2.3", "react-signature-canvas": "^1.0.6", "react-switch": "^7.0.0", "react-table": "^7.8.0", "reactflow": "^11.11.4", "recharts": "^2.15.3", "sharp": "^0.32.1", "smartystreets-javascript-sdk": "^5.0.1", "stripe": "^14.19.0"}, "devDependencies": {"autoprefixer": "^10.4.14", "cross-env": "^7.0.3", "eslint": "8.12.0", "eslint-config-next": "12.1.4", "postcss": "^8.4.24", "sass": "^1.58.3"}, "prettier": {"singleQuote": true, "semi": false, "trailingComma": "none", "tabWidth": 2, "printWidth": 140}}